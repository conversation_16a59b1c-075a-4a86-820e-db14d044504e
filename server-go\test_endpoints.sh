#!/bin/bash

# Test script for TuResiBo Go server endpoints
# Make sure the server is running on localhost:8000 before running this script

BASE_URL="http://localhost:8000"
API_BASE="${BASE_URL}/api/v1"

echo "🚀 Testing TuResiBo Go Server Endpoints"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local expected_status=$3
    local description=$4
    local data=$5

    echo -e "\n${YELLOW}Testing: ${description}${NC}"
    echo "Endpoint: ${method} ${endpoint}"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "${endpoint}")
    elif [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X POST -H "Content-Type: application/json" -d "$data" "${endpoint}")
    else
        response=$(curl -s -w "\n%{http_code}" -X "${method}" "${endpoint}")
    fi
    
    # Extract status code (last line)
    status_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} - Status: $status_code"
    else
        echo -e "${RED}❌ FAIL${NC} - Expected: $expected_status, Got: $status_code"
    fi
    
    # Show response body if it's not too long
    if [ ${#body} -lt 500 ]; then
        echo "Response: $body"
    else
        echo "Response: [Long response truncated]"
    fi
}

echo -e "\n📋 Starting endpoint tests..."

# Health check
test_endpoint "GET" "${BASE_URL}/health" "200" "Health check"

# Utility endpoints
test_endpoint "GET" "${API_BASE}/especialidades" "200" "Get especialidades"

# Videoclases endpoints
test_endpoint "GET" "${API_BASE}/videoclases" "200" "List videoclases"

# Videos cortos endpoints  
test_endpoint "GET" "${API_BASE}/videos-cortos" "200" "List videos cortos"

# Planeador endpoints
test_endpoint "GET" "${API_BASE}/planeador/lesson_reviews/by_user/test_user" "200" "Get lesson reviews by user"

# Test creating a lesson review
lesson_review_data='{"leccion_id": "test_leccion", "user_id": "test_user", "notes": "Test notes"}'
test_endpoint "POST" "${API_BASE}/planeador/lesson_reviews" "201" "Create lesson review" "$lesson_review_data"

# Test endpoints that should return 501 (Not Implemented)
test_endpoint "GET" "${API_BASE}/clinical-notes" "501" "Clinical notes (not implemented)"
test_endpoint "GET" "${API_BASE}/clinical-cases" "501" "Clinical cases (not implemented)"
test_endpoint "GET" "${API_BASE}/cuestionarios" "501" "Cuestionarios (not implemented)"
test_endpoint "GET" "${API_BASE}/flashcards" "501" "Flashcards (not implemented)"

echo -e "\n🏁 Test completed!"
echo "========================================"
echo -e "${YELLOW}Note: Some endpoints return 501 (Not Implemented) as expected${NC}"
echo -e "${YELLOW}File upload endpoints require multipart/form-data and are not tested here${NC}"
echo -e "\n${GREEN}To test file uploads, use a tool like Postman or curl with -F flag${NC}"

# Example curl command for file upload
echo -e "\n📝 Example file upload command:"
echo 'curl -X POST \'
echo '  http://localhost:8000/api/v1/upload-video \'
echo '  -F "especialidad=medicina" \'
echo '  -F "sistema=cardiovascular" \'
echo '  -F "tema=arritmias" \'
echo '  -F "titulo=Test Video" \'
echo '  -F "file=@/path/to/video.mp4"'
