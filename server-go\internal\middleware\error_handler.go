package middleware

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ErrorResponse represents an error response
type ErrorResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Error   string `json:"error,omitempty"`
}

// ErrorHandler is a middleware that handles panics and errors
func ErrorHandler() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			log.Printf("Panic recovered: %s", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Success: false,
				Message: "Internal server error",
				Error:   err,
			})
		} else {
			log.Printf("Panic recovered: %v", recovered)
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Success: false,
				Message: "Internal server error",
			})
		}
		c.Abort()
	})
}

// HandleError is a helper function to handle errors consistently
func HandleError(c *gin.Context, statusCode int, message string, err error) {
	log.Printf("Error: %s - %v", message, err)
	
	response := ErrorResponse{
		Success: false,
		Message: message,
	}
	
	if err != nil {
		response.Error = err.Error()
	}
	
	c.JSON(statusCode, response)
}

// HandleSuccess is a helper function to handle success responses consistently
func HandleSuccess(c *gin.Context, message string, data interface{}) {
	response := map[string]interface{}{
		"success": true,
		"message": message,
	}
	
	if data != nil {
		response["data"] = data
	}
	
	c.JSON(http.StatusOK, response)
}
