from typing import Optional

from sqlalchemy.orm import Session

from app.db.base_class import CRUDBase
from app.planeador.models import LessonReview
from app.planeador.schemas import LessonReviewCreate, LessonReviewUpdate


class CRUDLessonReview(CRUDBase[LessonReview, LessonReviewCreate, LessonReviewUpdate]):
    def get_by_user_and_lesson(
        self,
        db: Session,
        *,
        user_id: str,
        leccion_id: str
    ) -> Optional[LessonReview]:
        return (
            db.query(self.model)
            .filter(LessonReview.user_id == user_id, LessonReview.leccion_id == leccion_id)
            .first()
        )


lesson_review = CRUDLessonReview(LessonReview)