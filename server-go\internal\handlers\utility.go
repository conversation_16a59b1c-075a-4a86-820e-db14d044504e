package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"turesibo-server/internal/middleware"
)

// GetEspecialidades handles getting all unique especialidades
func (h *UploadHandler) GetEspecialidades(c *gin.Context) {
	especialidades, err := h.repo.GetUniqueEspecialidades()
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to get especialidades", err)
		return
	}

	response := map[string]interface{}{
		"especialidades": especialidades,
	}

	c.JSON(http.StatusOK, response)
}

// GetSistemasByEspecialidad handles getting sistemas for a specific especialidad
func (h *UploadHandler) GetSistemasByEspecialidad(c *gin.Context) {
	especialidad := c.Param("especialidad")
	if especialidad == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing especialidad parameter", nil)
		return
	}

	sistemas, err := h.repo.GetSistemasByEspecialidad(especialidad)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to get sistemas", err)
		return
	}

	response := map[string]interface{}{
		"sistemas": sistemas,
	}

	c.JSON(http.StatusOK, response)
}

// GetTemasByEspecialidadSistema handles getting temas for a specific especialidad and sistema
func (h *UploadHandler) GetTemasByEspecialidadSistema(c *gin.Context) {
	especialidad := c.Param("especialidad")
	sistema := c.Param("sistema")

	if especialidad == "" || sistema == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing especialidad or sistema parameter", nil)
		return
	}

	temas, err := h.repo.GetTemasByEspecialidadSistema(especialidad, sistema)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to get temas", err)
		return
	}

	response := map[string]interface{}{
		"temas": temas,
	}

	c.JSON(http.StatusOK, response)
}

// Placeholder handlers for clinical notes, cases, cuestionarios, and flashcards
// These would be implemented similarly to the video handlers above

func (h *UploadHandler) UploadClinicalNotes(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Clinical notes upload not implemented yet", nil)
}

func (h *UploadHandler) ListClinicalNotes(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Clinical notes list not implemented yet", nil)
}

func (h *UploadHandler) GetClinicalNote(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Get clinical note not implemented yet", nil)
}

func (h *UploadHandler) UploadClinicalCase(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Clinical case upload not implemented yet", nil)
}

func (h *UploadHandler) ListClinicalCases(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Clinical cases list not implemented yet", nil)
}

func (h *UploadHandler) GetClinicalCase(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Get clinical case not implemented yet", nil)
}

func (h *UploadHandler) UploadClinicalCasesJSON(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Clinical cases JSON upload not implemented yet", nil)
}

func (h *UploadHandler) UpdateClinicalCase(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Update clinical case not implemented yet", nil)
}

func (h *UploadHandler) DeleteClinicalCase(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Delete clinical case not implemented yet", nil)
}

func (h *UploadHandler) CreateCuestionario(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Create cuestionario not implemented yet", nil)
}

func (h *UploadHandler) ListCuestionarios(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "List cuestionarios not implemented yet", nil)
}

func (h *UploadHandler) GetCuestionario(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Get cuestionario not implemented yet", nil)
}

func (h *UploadHandler) UploadCuestionariosJSON(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Cuestionarios JSON upload not implemented yet", nil)
}

func (h *UploadHandler) UpdateCuestionario(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Update cuestionario not implemented yet", nil)
}

func (h *UploadHandler) DeleteCuestionario(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Delete cuestionario not implemented yet", nil)
}

func (h *UploadHandler) CreateFlashcard(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Create flashcard not implemented yet", nil)
}

func (h *UploadHandler) ListFlashcards(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "List flashcards not implemented yet", nil)
}

func (h *UploadHandler) GetFlashcard(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Get flashcard not implemented yet", nil)
}

func (h *UploadHandler) UploadFlashcardsJSON(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Flashcards JSON upload not implemented yet", nil)
}

func (h *UploadHandler) UpdateFlashcard(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Update flashcard not implemented yet", nil)
}

func (h *UploadHandler) DeleteFlashcard(c *gin.Context) {
	middleware.HandleError(c, http.StatusNotImplemented, "Delete flashcard not implemented yet", nil)
}
