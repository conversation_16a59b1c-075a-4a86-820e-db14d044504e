"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/lecciones/pediatria/page",{

/***/ "(app-pages-browser)/./app/lecciones/pediatria/page.tsx":
/*!******************************************!*\
  !*** ./app/lecciones/pediatria/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PediatriaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst especialidad = \"Pediatria\";\n// Helper function to get API base URL (same pattern as upload components)\nfunction getApiBaseUrl() {\n    var _process_env_NEXT_PUBLIC_API_BASE_URL;\n    const base = (_process_env_NEXT_PUBLIC_API_BASE_URL = \"http://localhost:8000/\") === null || _process_env_NEXT_PUBLIC_API_BASE_URL === void 0 ? void 0 : _process_env_NEXT_PUBLIC_API_BASE_URL.replace(/\\/$/, '');\n    return base || 'http://localhost:8000';\n}\nasync function fetchContent(contentType) {\n    try {\n        const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/contenido/\").concat(contentType, \"/?especialidad=\").concat(especialidad));\n        if (!response.ok) {\n            if (response.status === 404) {\n                // Return empty array if endpoint doesn't exist yet\n                return [];\n            }\n            throw new Error(\"Failed to fetch \".concat(contentType, \": \").concat(response.status, \" \").concat(response.statusText));\n        }\n        return response.json();\n    } catch (error) {\n        if (error instanceof TypeError && error.message.includes('fetch')) {\n            throw new Error(\"Network error while fetching \".concat(contentType, \". Make sure the server is running.\"));\n        }\n        throw error;\n    }\n}\nfunction ContentSection(param) {\n    let { title, data, basePath } = param;\n    if (!data || data.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-semibold mb-4\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"space-y-2\",\n                children: data.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"p-2 border rounded hover:bg-gray-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/lecciones/pediatria/\".concat(basePath, \"/\").concat(item.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: item.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this),\n                                \" -\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        item.sistema,\n                                        \" / \",\n                                        item.tema\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    }, item.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_c = ContentSection;\nfunction PediatriaPage() {\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        videoclases: null,\n        videos_cortos: null,\n        notas_clinicas: null,\n        casos_clinicos: null,\n        cuestionarios: null,\n        flashcards: null,\n        repaso: null\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewingCasosClinicosUI, setViewingCasosClinicosUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PediatriaPage.useEffect\": ()=>{\n            async function loadContent() {\n                try {\n                    const contentTypes = [\n                        \"videoclases\",\n                        \"videos-cortos\",\n                        \"notas-clinicas\",\n                        \"casos-clinicos\",\n                        \"cuestionarios\",\n                        \"flashcards\",\n                        \"repaso\"\n                    ];\n                    // Use Promise.allSettled to handle individual failures gracefully\n                    const results = await Promise.allSettled(contentTypes.map({\n                        \"PediatriaPage.useEffect.loadContent\": (type)=>fetchContent(type)\n                    }[\"PediatriaPage.useEffect.loadContent\"]));\n                    const newContent = {};\n                    const errors = [];\n                    contentTypes.forEach({\n                        \"PediatriaPage.useEffect.loadContent\": (type, index)=>{\n                            const result = results[index];\n                            if (result.status === 'fulfilled') {\n                                newContent[type.replace(/-/g, '_')] = result.value;\n                            } else {\n                                newContent[type.replace(/-/g, '_')] = [];\n                                errors.push(\"\".concat(type, \": \").concat(result.reason.message));\n                            }\n                        }\n                    }[\"PediatriaPage.useEffect.loadContent\"]);\n                    setContent(newContent);\n                    // Only show error if all requests failed\n                    if (errors.length === contentTypes.length) {\n                        setError(\"Failed to load content: \".concat(errors.join(', ')));\n                    } else if (errors.length > 0) {\n                        console.warn('Some content types failed to load:', errors);\n                    }\n                } catch (err) {\n                    if (err instanceof Error) {\n                        setError(err.message);\n                    } else {\n                        setError(\"An unknown error occurred\");\n                    }\n                } finally{\n                    setLoading(false);\n                }\n            }\n            loadContent();\n        }\n    }[\"PediatriaPage.useEffect\"], []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: \"Loading content...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n            lineNumber: 184,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: [\n                \"Error: \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 12\n        }, this);\n    }\n    // Check if any content is available\n    const hasAnyContent = Object.values(content).some((data)=>data && data.length > 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-6\",\n                children: \"Lecciones de Pediatr\\xeda\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            !hasAnyContent && !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-lg\",\n                        children: \"No hay contenido disponible para Pediatr\\xeda en este momento.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-sm mt-2\",\n                        children: \"El contenido se mostrar\\xe1 aqu\\xed una vez que sea subido al sistema.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Videoclases\",\n                data: content.videoclases,\n                basePath: \"videoclases\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Videos Cortos\",\n                data: content.videos_cortos,\n                basePath: \"videos-cortos\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Notas Cl\\xednicas\",\n                data: content.notas_clinicas,\n                basePath: \"notas-clinicas\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Casos Cl\\xednicos\",\n                data: content.casos_clinicos,\n                basePath: \"casos-clinicos\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Cuestionarios\",\n                data: content.cuestionarios,\n                basePath: \"cuestionarios\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Flashcards\",\n                data: content.flashcards,\n                basePath: \"flashcards\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Repaso\",\n                data: content.repaso,\n                basePath: \"repaso\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n_s(PediatriaPage, \"/h9WASC653+hBcUyTQrZWKrXoZ8=\");\n_c1 = PediatriaPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ContentSection\");\n$RefreshReg$(_c1, \"PediatriaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9sZWNjaW9uZXMvcGVkaWF0cmlhL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ2Y7QUFrRDdCLE1BQU1HLGVBQWU7QUFFckIsMEVBQTBFO0FBQzFFLFNBQVNDO1FBQ01DO0lBQWIsTUFBTUMsUUFBT0Qsd0NBQUFBLHdCQUFvQyxjQUFwQ0EsNERBQUFBLHNDQUFzQ0ksT0FBTyxDQUFDLE9BQU87SUFDbEUsT0FBT0gsUUFBUTtBQUNqQjtBQUVBLGVBQWVJLGFBQWdCQyxXQUFtQjtJQUNoRCxJQUFJO1FBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUNyQixHQUF1Q0YsT0FBcENQLGlCQUFnQixzQkFBaURELE9BQTdCUSxhQUFZLG1CQUE4QixPQUFiUjtRQUV0RSxJQUFJLENBQUNTLFNBQVNFLEVBQUUsRUFBRTtZQUNoQixJQUFJRixTQUFTRyxNQUFNLEtBQUssS0FBSztnQkFDM0IsbURBQW1EO2dCQUNuRCxPQUFPLEVBQUU7WUFDWDtZQUNBLE1BQU0sSUFBSUMsTUFBTSxtQkFBbUNKLE9BQWhCRCxhQUFZLE1BQXVCQyxPQUFuQkEsU0FBU0csTUFBTSxFQUFDLEtBQXVCLE9BQXBCSCxTQUFTSyxVQUFVO1FBQzNGO1FBQ0EsT0FBT0wsU0FBU00sSUFBSTtJQUN0QixFQUFFLE9BQU9DLE9BQU87UUFDZCxJQUFJQSxpQkFBaUJDLGFBQWFELE1BQU1FLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLFVBQVU7WUFDakUsTUFBTSxJQUFJTixNQUFNLGdDQUE0QyxPQUFaTCxhQUFZO1FBQzlEO1FBQ0EsTUFBTVE7SUFDUjtBQUNGO0FBRUEsU0FBU0ksZUFBc0MsS0FROUM7UUFSOEMsRUFDN0NDLEtBQUssRUFDTEMsSUFBSSxFQUNKQyxRQUFRLEVBS1QsR0FSOEM7SUFTN0MsSUFBSSxDQUFDRCxRQUFRQSxLQUFLRSxNQUFNLEtBQUssR0FBRztRQUM5QixPQUFPO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ0M7UUFBUUMsV0FBVTs7MEJBQ2pCLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBK0JMOzs7Ozs7MEJBQzdDLDhEQUFDTztnQkFBR0YsV0FBVTswQkFDWEosS0FBS08sR0FBRyxDQUFDLENBQUNDLHFCQUNULDhEQUFDQzt3QkFBaUJMLFdBQVU7a0NBQzFCLDRFQUFDM0Isa0RBQUlBOzRCQUFDaUMsTUFBTSx3QkFBb0NGLE9BQVpQLFVBQVMsS0FBVyxPQUFSTyxLQUFLRyxFQUFFOzs4Q0FDckQsOERBQUNDO29DQUFLUixXQUFVOzhDQUFlSSxLQUFLVCxLQUFLOzs7Ozs7Z0NBQVE7Z0NBQUc7OENBQ3BELDhEQUFDYTtvQ0FBS1IsV0FBVTs7d0NBQ2JJLEtBQUtLLE9BQU87d0NBQUM7d0NBQUlMLEtBQUtNLElBQUk7Ozs7Ozs7Ozs7Ozs7dUJBSnhCTixLQUFLRyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O0FBWTFCO0tBOUJTYjtBQWdDTSxTQUFTaUI7O0lBQ3RCLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHekMsK0NBQVFBLENBQTBDO1FBQzlFMEMsYUFBYTtRQUNiQyxlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMsZ0JBQWdCO1FBQ2hCQyxlQUFlO1FBQ2ZDLFlBQVk7UUFDWkMsUUFBUTtJQUNWO0lBQ0EsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdsRCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNrQixPQUFPaUMsU0FBUyxHQUFHbkQsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ29ELHdCQUF3QkMsMEJBQTBCLEdBQUdyRCwrQ0FBUUEsQ0FBQztJQUVyRUQsZ0RBQVNBO21DQUFDO1lBQ1IsZUFBZXVEO2dCQUNiLElBQUk7b0JBQ0YsTUFBTUMsZUFBZTt3QkFDbkI7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7cUJBQ0Q7b0JBRUQsa0VBQWtFO29CQUNsRSxNQUFNQyxVQUFVLE1BQU1DLFFBQVFDLFVBQVUsQ0FDdENILGFBQWF4QixHQUFHOytEQUFDNEIsQ0FBQUEsT0FBUWxELGFBQTBCa0Q7O29CQUdyRCxNQUFNQyxhQUFzRCxDQUFDO29CQUM3RCxNQUFNQyxTQUFtQixFQUFFO29CQUUzQk4sYUFBYU8sT0FBTzsrREFBQyxDQUFDSCxNQUFNSTs0QkFDMUIsTUFBTUMsU0FBU1IsT0FBTyxDQUFDTyxNQUFNOzRCQUM3QixJQUFJQyxPQUFPbEQsTUFBTSxLQUFLLGFBQWE7Z0NBQ2pDOEMsVUFBVSxDQUFDRCxLQUFLbkQsT0FBTyxDQUFDLE1BQU0sS0FBSyxHQUFHd0QsT0FBT0MsS0FBSzs0QkFDcEQsT0FBTztnQ0FDTEwsVUFBVSxDQUFDRCxLQUFLbkQsT0FBTyxDQUFDLE1BQU0sS0FBSyxHQUFHLEVBQUU7Z0NBQ3hDcUQsT0FBT0ssSUFBSSxDQUFDLEdBQVlGLE9BQVRMLE1BQUssTUFBMEIsT0FBdEJLLE9BQU9HLE1BQU0sQ0FBQy9DLE9BQU87NEJBQy9DO3dCQUNGOztvQkFFQXFCLFdBQVdtQjtvQkFFWCx5Q0FBeUM7b0JBQ3pDLElBQUlDLE9BQU9uQyxNQUFNLEtBQUs2QixhQUFhN0IsTUFBTSxFQUFFO3dCQUN6Q3lCLFNBQVMsMkJBQTZDLE9BQWxCVSxPQUFPTyxJQUFJLENBQUM7b0JBQ2xELE9BQU8sSUFBSVAsT0FBT25DLE1BQU0sR0FBRyxHQUFHO3dCQUM1QjJDLFFBQVFDLElBQUksQ0FBQyxzQ0FBc0NUO29CQUNyRDtnQkFFRixFQUFFLE9BQU9VLEtBQUs7b0JBQ1osSUFBSUEsZUFBZXhELE9BQU87d0JBQ3hCb0MsU0FBU29CLElBQUluRCxPQUFPO29CQUN0QixPQUFPO3dCQUNMK0IsU0FBUztvQkFDWDtnQkFDRixTQUFVO29CQUNSRCxXQUFXO2dCQUNiO1lBQ0Y7WUFFQUk7UUFDRjtrQ0FBRyxFQUFFO0lBRUwsSUFBSUwsU0FBUztRQUNYLHFCQUFPLDhEQUFDdUI7WUFBSTVDLFdBQVU7c0JBQXdCOzs7Ozs7SUFDaEQ7SUFFQSxJQUFJVixPQUFPO1FBQ1QscUJBQU8sOERBQUNzRDtZQUFJNUMsV0FBVTs7Z0JBQXdCO2dCQUFRVjs7Ozs7OztJQUN4RDtJQUVBLG9DQUFvQztJQUNwQyxNQUFNdUQsZ0JBQWdCQyxPQUFPQyxNQUFNLENBQUNuQyxTQUFTb0MsSUFBSSxDQUFDcEQsQ0FBQUEsT0FBUUEsUUFBUUEsS0FBS0UsTUFBTSxHQUFHO0lBRWhGLHFCQUNFLDhEQUFDOEM7UUFBSTVDLFdBQVU7OzBCQUNiLDhEQUFDaUQ7Z0JBQUdqRCxXQUFVOzBCQUEwQjs7Ozs7O1lBRXZDLENBQUM2QyxpQkFBaUIsQ0FBQ3hCLFdBQVcsQ0FBQy9CLHVCQUM5Qiw4REFBQ3NEO2dCQUFJNUMsV0FBVTs7a0NBQ2IsOERBQUNrRDt3QkFBRWxELFdBQVU7a0NBQXdCOzs7Ozs7a0NBR3JDLDhEQUFDa0Q7d0JBQUVsRCxXQUFVO2tDQUE2Qjs7Ozs7Ozs7Ozs7OzBCQU05Qyw4REFBQ047Z0JBQ0NDLE9BQU07Z0JBQ05DLE1BQU1nQixRQUFRRSxXQUFXO2dCQUN6QmpCLFVBQVM7Ozs7OzswQkFFWCw4REFBQ0g7Z0JBQ0NDLE9BQU07Z0JBQ05DLE1BQU1nQixRQUFRRyxhQUFhO2dCQUMzQmxCLFVBQVM7Ozs7OzswQkFFWCw4REFBQ0g7Z0JBQ0NDLE9BQU07Z0JBQ05DLE1BQU1nQixRQUFRSSxjQUFjO2dCQUM1Qm5CLFVBQVM7Ozs7OzswQkFFWCw4REFBQ0g7Z0JBQ0NDLE9BQU07Z0JBQ05DLE1BQU1nQixRQUFRSyxjQUFjO2dCQUM1QnBCLFVBQVM7Ozs7OzswQkFFWCw4REFBQ0g7Z0JBQ0NDLE9BQU07Z0JBQ05DLE1BQU1nQixRQUFRTSxhQUFhO2dCQUMzQnJCLFVBQVM7Ozs7OzswQkFFWCw4REFBQ0g7Z0JBQ0NDLE9BQU07Z0JBQ05DLE1BQU1nQixRQUFRTyxVQUFVO2dCQUN4QnRCLFVBQVM7Ozs7OzswQkFFWCw4REFBQ0g7Z0JBQ0NDLE9BQU07Z0JBQ05DLE1BQU1nQixRQUFRUSxNQUFNO2dCQUNwQnZCLFVBQVM7Ozs7Ozs7Ozs7OztBQUlqQjtHQW5Jd0JjO01BQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFZJQ09cXE9uZURyaXZlXFxEb2N1bWVudG9zXFwwLiBUdVJlc2lCb1xcY2xpZW50XFxhcHBcXGxlY2Npb25lc1xccGVkaWF0cmlhXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xyXG5pbXBvcnQgQ2Fzb3NDbGluaWNvc1VJIGZyb20gXCJAL2NvbXBvbmVudHMvY2Fzb3NjbGluaWNvc191aVwiO1xyXG5cclxuLy8gRGVmaW5lIGludGVyZmFjZXMgZm9yIHRoZSBjb250ZW50IHR5cGVzXHJcbmludGVyZmFjZSBDb250ZW50SXRlbSB7XHJcbiAgaWQ6IG51bWJlcjtcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIHRlbWE6IHN0cmluZztcclxuICBzaXN0ZW1hOiBzdHJpbmc7XHJcbn1cclxuXHJcbmludGVyZmFjZSBJbWFnZURhdGEge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgdXJsOiBzdHJpbmc7XHJcbiAgZmlsZW5hbWU/OiBzdHJpbmc7XHJcbiAgZmlsZV9wYXRoPzogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgT3BjaW9uRGF0YSB7XHJcbiAgaWQ6IHN0cmluZztcclxuICBvcGNpb246IHN0cmluZztcclxuICBlc19jb3JyZWN0YTogYm9vbGVhbjtcclxuICBleHBsaWNhY2lvbjogc3RyaW5nO1xyXG4gIGltYWdlbl91cmw/OiBzdHJpbmc7XHJcbiAgaW1hZ2VuX3BhdGg/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmludGVyZmFjZSBQcmVndW50YURhdGEge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgY2Fzb19jbGluaWNvOiBzdHJpbmc7XHJcbiAgcHJlZ3VudGE6IHN0cmluZztcclxuICBpbWFnZW5fdXJsPzogc3RyaW5nO1xyXG4gIGltYWdlbl9wYXRoPzogc3RyaW5nO1xyXG4gIG9wY2lvbmVzOiBPcGNpb25EYXRhW107XHJcbiAgcmVzcHVlc3RhX2NvcnJlY3RhOiBzdHJpbmc7XHJcbiAgZXhwbGljYWNpb25fZ2VuZXJhbDogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgQ2Fzb0NsaW5pY28ge1xyXG4gIGlkOiBudW1iZXI7XHJcbiAgdGl0bGU6IHN0cmluZztcclxuICBlc3BlY2lhbGlkYWQ6IHN0cmluZztcclxuICBzaXN0ZW1hOiBzdHJpbmc7XHJcbiAgdGVtYTogc3RyaW5nO1xyXG4gIGRlc2NyaXBjaW9uOiBzdHJpbmc7XHJcbiAgaW1hZ2VzPzogSW1hZ2VEYXRhW107XHJcbiAgcHJlZ3VudGFzOiBQcmVndW50YURhdGFbXTtcclxuICBjcmVhdGVkX2F0OiBzdHJpbmc7XHJcbn1cclxuXHJcbmNvbnN0IGVzcGVjaWFsaWRhZCA9IFwiUGVkaWF0cmlhXCI7XHJcblxyXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gZ2V0IEFQSSBiYXNlIFVSTCAoc2FtZSBwYXR0ZXJuIGFzIHVwbG9hZCBjb21wb25lbnRzKVxyXG5mdW5jdGlvbiBnZXRBcGlCYXNlVXJsKCk6IHN0cmluZyB7XHJcbiAgY29uc3QgYmFzZSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9CQVNFX1VSTD8ucmVwbGFjZSgvXFwvJC8sICcnKTtcclxuICByZXR1cm4gYmFzZSB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDAwJztcclxufVxyXG5cclxuYXN5bmMgZnVuY3Rpb24gZmV0Y2hDb250ZW50PFQ+KGNvbnRlbnRUeXBlOiBzdHJpbmcpOiBQcm9taXNlPFRbXT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKFxyXG4gICAgICBgJHtnZXRBcGlCYXNlVXJsKCl9L2FwaS92MS9jb250ZW5pZG8vJHtjb250ZW50VHlwZX0vP2VzcGVjaWFsaWRhZD0ke2VzcGVjaWFsaWRhZH1gXHJcbiAgICApO1xyXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDQpIHtcclxuICAgICAgICAvLyBSZXR1cm4gZW1wdHkgYXJyYXkgaWYgZW5kcG9pbnQgZG9lc24ndCBleGlzdCB5ZXRcclxuICAgICAgICByZXR1cm4gW107XHJcbiAgICAgIH1cclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggJHtjb250ZW50VHlwZX06ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBUeXBlRXJyb3IgJiYgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnZmV0Y2gnKSkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYE5ldHdvcmsgZXJyb3Igd2hpbGUgZmV0Y2hpbmcgJHtjb250ZW50VHlwZX0uIE1ha2Ugc3VyZSB0aGUgc2VydmVyIGlzIHJ1bm5pbmcuYCk7XHJcbiAgICB9XHJcbiAgICB0aHJvdyBlcnJvcjtcclxuICB9XHJcbn1cclxuXHJcbmZ1bmN0aW9uIENvbnRlbnRTZWN0aW9uPFQgZXh0ZW5kcyBDb250ZW50SXRlbT4oe1xyXG4gIHRpdGxlLFxyXG4gIGRhdGEsXHJcbiAgYmFzZVBhdGgsXHJcbn06IHtcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIGRhdGE6IFRbXSB8IG51bGw7XHJcbiAgYmFzZVBhdGg6IHN0cmluZztcclxufSkge1xyXG4gIGlmICghZGF0YSB8fCBkYXRhLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwibWItOFwiPlxyXG4gICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCBtYi00XCI+e3RpdGxlfTwvaDI+XHJcbiAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICB7ZGF0YS5tYXAoKGl0ZW0pID0+IChcclxuICAgICAgICAgIDxsaSBrZXk9e2l0ZW0uaWR9IGNsYXNzTmFtZT1cInAtMiBib3JkZXIgcm91bmRlZCBob3ZlcjpiZy1ncmF5LTEwMFwiPlxyXG4gICAgICAgICAgICA8TGluayBocmVmPXtgL2xlY2Npb25lcy9wZWRpYXRyaWEvJHtiYXNlUGF0aH0vJHtpdGVtLmlkfWB9PlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2l0ZW0udGl0bGV9PC9zcGFuPiAte1wiIFwifVxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAge2l0ZW0uc2lzdGVtYX0gLyB7aXRlbS50ZW1hfVxyXG4gICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgPC9saT5cclxuICAgICAgICApKX1cclxuICAgICAgPC91bD5cclxuICAgIDwvc2VjdGlvbj5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQZWRpYXRyaWFQYWdlKCkge1xyXG4gIGNvbnN0IFtjb250ZW50LCBzZXRDb250ZW50XSA9IHVzZVN0YXRlPHsgW2tleTogc3RyaW5nXTogQ29udGVudEl0ZW1bXSB8IG51bGwgfT4oe1xyXG4gICAgdmlkZW9jbGFzZXM6IG51bGwsXHJcbiAgICB2aWRlb3NfY29ydG9zOiBudWxsLFxyXG4gICAgbm90YXNfY2xpbmljYXM6IG51bGwsXHJcbiAgICBjYXNvc19jbGluaWNvczogbnVsbCxcclxuICAgIGN1ZXN0aW9uYXJpb3M6IG51bGwsXHJcbiAgICBmbGFzaGNhcmRzOiBudWxsLFxyXG4gICAgcmVwYXNvOiBudWxsLFxyXG4gIH0pO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW3ZpZXdpbmdDYXNvc0NsaW5pY29zVUksIHNldFZpZXdpbmdDYXNvc0NsaW5pY29zVUldID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgYXN5bmMgZnVuY3Rpb24gbG9hZENvbnRlbnQoKSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgY29udGVudFR5cGVzID0gW1xyXG4gICAgICAgICAgXCJ2aWRlb2NsYXNlc1wiLFxyXG4gICAgICAgICAgXCJ2aWRlb3MtY29ydG9zXCIsXHJcbiAgICAgICAgICBcIm5vdGFzLWNsaW5pY2FzXCIsXHJcbiAgICAgICAgICBcImNhc29zLWNsaW5pY29zXCIsXHJcbiAgICAgICAgICBcImN1ZXN0aW9uYXJpb3NcIixcclxuICAgICAgICAgIFwiZmxhc2hjYXJkc1wiLFxyXG4gICAgICAgICAgXCJyZXBhc29cIixcclxuICAgICAgICBdO1xyXG5cclxuICAgICAgICAvLyBVc2UgUHJvbWlzZS5hbGxTZXR0bGVkIHRvIGhhbmRsZSBpbmRpdmlkdWFsIGZhaWx1cmVzIGdyYWNlZnVsbHlcclxuICAgICAgICBjb25zdCByZXN1bHRzID0gYXdhaXQgUHJvbWlzZS5hbGxTZXR0bGVkKFxyXG4gICAgICAgICAgY29udGVudFR5cGVzLm1hcCh0eXBlID0+IGZldGNoQ29udGVudDxDb250ZW50SXRlbT4odHlwZSkpXHJcbiAgICAgICAgKTtcclxuXHJcbiAgICAgICAgY29uc3QgbmV3Q29udGVudDogeyBba2V5OiBzdHJpbmddOiBDb250ZW50SXRlbVtdIHwgbnVsbCB9ID0ge307XHJcbiAgICAgICAgY29uc3QgZXJyb3JzOiBzdHJpbmdbXSA9IFtdO1xyXG5cclxuICAgICAgICBjb250ZW50VHlwZXMuZm9yRWFjaCgodHlwZSwgaW5kZXgpID0+IHtcclxuICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHJlc3VsdHNbaW5kZXhdO1xyXG4gICAgICAgICAgaWYgKHJlc3VsdC5zdGF0dXMgPT09ICdmdWxmaWxsZWQnKSB7XHJcbiAgICAgICAgICAgIG5ld0NvbnRlbnRbdHlwZS5yZXBsYWNlKC8tL2csICdfJyldID0gcmVzdWx0LnZhbHVlO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgbmV3Q29udGVudFt0eXBlLnJlcGxhY2UoLy0vZywgJ18nKV0gPSBbXTtcclxuICAgICAgICAgICAgZXJyb3JzLnB1c2goYCR7dHlwZX06ICR7cmVzdWx0LnJlYXNvbi5tZXNzYWdlfWApO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBzZXRDb250ZW50KG5ld0NvbnRlbnQpO1xyXG5cclxuICAgICAgICAvLyBPbmx5IHNob3cgZXJyb3IgaWYgYWxsIHJlcXVlc3RzIGZhaWxlZFxyXG4gICAgICAgIGlmIChlcnJvcnMubGVuZ3RoID09PSBjb250ZW50VHlwZXMubGVuZ3RoKSB7XHJcbiAgICAgICAgICBzZXRFcnJvcihgRmFpbGVkIHRvIGxvYWQgY29udGVudDogJHtlcnJvcnMuam9pbignLCAnKX1gKTtcclxuICAgICAgICB9IGVsc2UgaWYgKGVycm9ycy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ1NvbWUgY29udGVudCB0eXBlcyBmYWlsZWQgdG8gbG9hZDonLCBlcnJvcnMpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICAgIGlmIChlcnIgaW5zdGFuY2VvZiBFcnJvcikge1xyXG4gICAgICAgICAgc2V0RXJyb3IoZXJyLm1lc3NhZ2UpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBzZXRFcnJvcihcIkFuIHVua25vd24gZXJyb3Igb2NjdXJyZWRcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgbG9hZENvbnRlbnQoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIGlmIChsb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBwLTRcIj5Mb2FkaW5nIGNvbnRlbnQuLi48L2Rpdj47XHJcbiAgfVxyXG5cclxuICBpZiAoZXJyb3IpIHtcclxuICAgIHJldHVybiA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHAtNFwiPkVycm9yOiB7ZXJyb3J9PC9kaXY+O1xyXG4gIH1cclxuXHJcbiAgLy8gQ2hlY2sgaWYgYW55IGNvbnRlbnQgaXMgYXZhaWxhYmxlXHJcbiAgY29uc3QgaGFzQW55Q29udGVudCA9IE9iamVjdC52YWx1ZXMoY29udGVudCkuc29tZShkYXRhID0+IGRhdGEgJiYgZGF0YS5sZW5ndGggPiAwKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcC00XCI+XHJcbiAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgbWItNlwiPkxlY2Npb25lcyBkZSBQZWRpYXRyw61hPC9oMT5cclxuXHJcbiAgICAgIHshaGFzQW55Q29udGVudCAmJiAhbG9hZGluZyAmJiAhZXJyb3IgJiYgKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LWxnXCI+XHJcbiAgICAgICAgICAgIE5vIGhheSBjb250ZW5pZG8gZGlzcG9uaWJsZSBwYXJhIFBlZGlhdHLDrWEgZW4gZXN0ZSBtb21lbnRvLlxyXG4gICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCB0ZXh0LXNtIG10LTJcIj5cclxuICAgICAgICAgICAgRWwgY29udGVuaWRvIHNlIG1vc3RyYXLDoSBhcXXDrSB1bmEgdmV6IHF1ZSBzZWEgc3ViaWRvIGFsIHNpc3RlbWEuXHJcbiAgICAgICAgICA8L3A+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICA8Q29udGVudFNlY3Rpb25cclxuICAgICAgICB0aXRsZT1cIlZpZGVvY2xhc2VzXCJcclxuICAgICAgICBkYXRhPXtjb250ZW50LnZpZGVvY2xhc2VzfVxyXG4gICAgICAgIGJhc2VQYXRoPVwidmlkZW9jbGFzZXNcIlxyXG4gICAgICAvPlxyXG4gICAgICA8Q29udGVudFNlY3Rpb25cclxuICAgICAgICB0aXRsZT1cIlZpZGVvcyBDb3J0b3NcIlxyXG4gICAgICAgIGRhdGE9e2NvbnRlbnQudmlkZW9zX2NvcnRvc31cclxuICAgICAgICBiYXNlUGF0aD1cInZpZGVvcy1jb3J0b3NcIlxyXG4gICAgICAvPlxyXG4gICAgICA8Q29udGVudFNlY3Rpb25cclxuICAgICAgICB0aXRsZT1cIk5vdGFzIENsw61uaWNhc1wiXHJcbiAgICAgICAgZGF0YT17Y29udGVudC5ub3Rhc19jbGluaWNhc31cclxuICAgICAgICBiYXNlUGF0aD1cIm5vdGFzLWNsaW5pY2FzXCJcclxuICAgICAgLz5cclxuICAgICAgPENvbnRlbnRTZWN0aW9uXHJcbiAgICAgICAgdGl0bGU9XCJDYXNvcyBDbMOtbmljb3NcIlxyXG4gICAgICAgIGRhdGE9e2NvbnRlbnQuY2Fzb3NfY2xpbmljb3N9XHJcbiAgICAgICAgYmFzZVBhdGg9XCJjYXNvcy1jbGluaWNvc1wiXHJcbiAgICAgIC8+XHJcbiAgICAgIDxDb250ZW50U2VjdGlvblxyXG4gICAgICAgIHRpdGxlPVwiQ3Vlc3Rpb25hcmlvc1wiXHJcbiAgICAgICAgZGF0YT17Y29udGVudC5jdWVzdGlvbmFyaW9zfVxyXG4gICAgICAgIGJhc2VQYXRoPVwiY3Vlc3Rpb25hcmlvc1wiXHJcbiAgICAgIC8+XHJcbiAgICAgIDxDb250ZW50U2VjdGlvblxyXG4gICAgICAgIHRpdGxlPVwiRmxhc2hjYXJkc1wiXHJcbiAgICAgICAgZGF0YT17Y29udGVudC5mbGFzaGNhcmRzfVxyXG4gICAgICAgIGJhc2VQYXRoPVwiZmxhc2hjYXJkc1wiXHJcbiAgICAgIC8+XHJcbiAgICAgIDxDb250ZW50U2VjdGlvblxyXG4gICAgICAgIHRpdGxlPVwiUmVwYXNvXCJcclxuICAgICAgICBkYXRhPXtjb250ZW50LnJlcGFzb31cclxuICAgICAgICBiYXNlUGF0aD1cInJlcGFzb1wiXHJcbiAgICAgIC8+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiTGluayIsImVzcGVjaWFsaWRhZCIsImdldEFwaUJhc2VVcmwiLCJwcm9jZXNzIiwiYmFzZSIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9CQVNFX1VSTCIsInJlcGxhY2UiLCJmZXRjaENvbnRlbnQiLCJjb250ZW50VHlwZSIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsInN0YXR1cyIsIkVycm9yIiwic3RhdHVzVGV4dCIsImpzb24iLCJlcnJvciIsIlR5cGVFcnJvciIsIm1lc3NhZ2UiLCJpbmNsdWRlcyIsIkNvbnRlbnRTZWN0aW9uIiwidGl0bGUiLCJkYXRhIiwiYmFzZVBhdGgiLCJsZW5ndGgiLCJzZWN0aW9uIiwiY2xhc3NOYW1lIiwiaDIiLCJ1bCIsIm1hcCIsIml0ZW0iLCJsaSIsImhyZWYiLCJpZCIsInNwYW4iLCJzaXN0ZW1hIiwidGVtYSIsIlBlZGlhdHJpYVBhZ2UiLCJjb250ZW50Iiwic2V0Q29udGVudCIsInZpZGVvY2xhc2VzIiwidmlkZW9zX2NvcnRvcyIsIm5vdGFzX2NsaW5pY2FzIiwiY2Fzb3NfY2xpbmljb3MiLCJjdWVzdGlvbmFyaW9zIiwiZmxhc2hjYXJkcyIsInJlcGFzbyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2V0RXJyb3IiLCJ2aWV3aW5nQ2Fzb3NDbGluaWNvc1VJIiwic2V0Vmlld2luZ0Nhc29zQ2xpbmljb3NVSSIsImxvYWRDb250ZW50IiwiY29udGVudFR5cGVzIiwicmVzdWx0cyIsIlByb21pc2UiLCJhbGxTZXR0bGVkIiwidHlwZSIsIm5ld0NvbnRlbnQiLCJlcnJvcnMiLCJmb3JFYWNoIiwiaW5kZXgiLCJyZXN1bHQiLCJ2YWx1ZSIsInB1c2giLCJyZWFzb24iLCJqb2luIiwiY29uc29sZSIsIndhcm4iLCJlcnIiLCJkaXYiLCJoYXNBbnlDb250ZW50IiwiT2JqZWN0IiwidmFsdWVzIiwic29tZSIsImgxIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/lecciones/pediatria/page.tsx\n"));

/***/ })

});