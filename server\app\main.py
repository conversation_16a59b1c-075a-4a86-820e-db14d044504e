from fastapi import FastAPI

from .core.config import get_settings
from .core.cors import add_cors_middleware
from .db.models import Base
from .db.session import get_engine
from .upload_content import models as _upload_content_models  # noqa: F401 ensure model import
from .planeador import models  # noqa: F401 ensure model import
from .api.api import api_router


def create_app() -> FastAPI:
    settings = get_settings()

    application = FastAPI(
        title=settings.PROJECT_NAME,
        version=settings.VERSION,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
    )

    add_cors_middleware(application)
    application.include_router(api_router, prefix=settings.API_V1_STR)

    @application.get("/health", tags=["health"])  # simple health-check
    async def health() -> dict:
        return {"status": "ok"}

    @application.on_event("startup")
    async def on_startup() -> None:
        # Create tables if a database is configured
        if get_settings().DATABASE_URL:
            engine = get_engine()
            Base.metadata.create_all(bind=engine)

    return application


app = create_app()


