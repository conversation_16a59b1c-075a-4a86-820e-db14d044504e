package database

import (
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"turesibo-server/internal/models"
)

// Initialize creates a database connection and runs migrations
func Initialize(databaseURL string) (*gorm.DB, error) {
	if databaseURL == "" {
		log.Fatal("DATABASE_URL is not set")
	}

	// Configure GORM logger
	gormLogger := logger.Default.LogMode(logger.Info)

	// Connect to database
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, err
	}

	// Test connection
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	if err := sqlDB.Ping(); err != nil {
		return nil, err
	}

	// Configure connection pool
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)

	// Auto-migrate models
	if err := autoMigrate(db); err != nil {
		return nil, err
	}

	log.Println("Database connection established and migrations completed")
	return db, nil
}

// autoMigrate runs database migrations for all models
func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		// Upload content models
		&models.VideoclasesData{},
		&models.VideosCortosData{},
		&models.NotasClinicasData{},
		&models.CasosClinicosData{},
		&models.CuestionariosData{},
		&models.FlashcardsData{},
		&models.RepasoData{},

		// Planeador models
		&models.LessonReview{},
	)
}
