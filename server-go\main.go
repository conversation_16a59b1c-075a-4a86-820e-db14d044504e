package main

import (
	"log"
	"net/http"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"gorm.io/gorm"

	"turesibo-server/internal/config"
	"turesibo-server/internal/database"
	"turesibo-server/internal/handlers"
	"turesibo-server/internal/middleware"
	"turesibo-server/internal/services"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Load configuration
	cfg := config.Load()

	// Initialize database
	var db *gorm.DB
	if cfg.DatabaseURL != "" {
		var err error
		db, err = database.Initialize(cfg.DatabaseURL)
		if err != nil {
			log.Printf("Warning: Failed to initialize database: %v", err)
			log.Println("Continuing without database connection...")
		} else {
			log.Println("Database connection established successfully")
		}
	} else {
		log.Println("No database URL provided, running without database")
	}

	// Initialize services
	var gcsService *services.GCSService
	if cfg.GoogleCloudProjectID != "" && cfg.GoogleCloudStorageBucket != "" {
		var err error
		gcsService, err = services.NewGCSService(cfg)
		if err != nil {
			log.Printf("Warning: Failed to initialize GCS service: %v", err)
			log.Println("Continuing without GCS service...")
		} else {
			log.Println("GCS service initialized successfully")
		}
	} else {
		log.Println("No GCS configuration provided, running without GCS service")
	}

	// Initialize handlers
	uploadHandler := handlers.NewUploadHandler(db, gcsService)
	planeadorHandler := handlers.NewPlaneadorHandler(db)

	// Setup Gin router
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.ErrorHandler())

	// CORS configuration
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = cfg.BackendCorsOrigins
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"}
	corsConfig.AllowHeaders = []string{
		"Origin",
		"Content-Type",
		"Accept",
		"Authorization",
		"X-Requested-With",
		"Access-Control-Allow-Origin",
		"Access-Control-Allow-Headers",
		"Access-Control-Allow-Methods",
	}
	corsConfig.ExposeHeaders = []string{
		"Content-Length",
		"Access-Control-Allow-Origin",
		"Access-Control-Allow-Headers",
		"Cache-Control",
		"Content-Language",
		"Content-Type",
	}
	corsConfig.AllowCredentials = true
	corsConfig.MaxAge = 12 * 3600 // 12 hours

	// Log CORS configuration for debugging
	log.Printf("CORS Origins: %v", cfg.BackendCorsOrigins)

	router.Use(cors.New(corsConfig))

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// API routes
	api := router.Group(cfg.APIV1Str)
	{
		// Upload content routes
		uploadHandler.RegisterRoutes(api)

		// Planeador routes
		planeadorHandler.RegisterRoutes(api)
	}

	// Start server
	port := cfg.Port
	if port == "" {
		port = "8000"
	}

	log.Printf("Starting server on port %s", port)
	log.Printf("Environment: %s", cfg.Environment)
	log.Printf("API prefix: %s", cfg.APIV1Str)

	if err := router.Run(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
