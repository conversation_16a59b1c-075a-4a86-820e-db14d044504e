import {  Calendar, 
  Scissors, <PERSON>ethoscope, Baby, Users, Heart, 
  FileText, Clapperboard, FileQuestion, Layers 
} from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Card, CardContent, } from "@/components/ui/card"

import TopProvider from "@/providers/top-provider";

export default function Component() {
  return (
    <div className="min-h-screen bg-white">
      <TopProvider />
      {/* Main Content */}
      <div className="p-8 max-w-7xl mx-auto">
        {/* Welcome Section */}
        <div className="flex justify-between items-center mb-12">
          <h1 className="text-2xl font-light text-gray-800">Bienvenido Dr. Juan, <PERSON>uenas <PERSON>.</h1>
          <Button variant="ghost" className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 font-medium">
            Mi Progreso
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Quick Access Cards */}
          {[
            { title: "Notas Clínicas", href: "/upload", icon: FileText, color: "text-blue-600", bgColor: "bg-blue-50" },
            { title: "Video Clases", href: "/upload", icon: Clapperboard, color: "text-red-600", bgColor: "bg-red-50" },
            { title: "Cuestionarios y Casos Clínicos", href: "/upload", icon: FileQuestion, color: "text-green-600", bgColor: "bg-green-50" },
            { title: "Flashcards", href: "/upload", icon: Layers, color: "text-purple-600", bgColor: "bg-purple-50" },
          ].map((card, index) => (
            <Link key={index} href={card.href} passHref>
              <Card className={`overflow-hidden hover:shadow-lg transition-shadow duration-300 ${card.bgColor} border-none`}>
                <CardContent className="p-6 flex flex-col items-center text-center">
                  <card.icon className={`w-12 h-12 mb-4 ${card.color}`} />
                  <h3 className="text-lg font-semibold text-gray-800">{card.title}</h3>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Study Planner */}
          <div className="lg:col-span-3 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-8 text-center hover:from-emerald-100 hover:to-emerald-200 transition-all duration-300 cursor-pointer group">
            <div className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-300">
              <Calendar className="w-10 h-10 text-emerald-600" />
            </div>
            <h3 className="font-medium text-emerald-800 text-xl mb-3">Planificador de Estudios</h3>
            <p className="text-emerald-600 mb-6">Organiza tu tiempo de estudio por especialidad</p>
            <Button className="w-full max-w-xs mx-auto bg-emerald-600 hover:bg-emerald-700 text-white rounded-xl py-3 font-medium shadow-none">
              Crear Plan de Estudio
            </Button>
          </div>
        </div>

        {/* Categories Section */}
        <div className="mb-12">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-light text-gray-800">Especialidades de Residencia</h2>
            <div className="flex items-center gap-12 text-sm text-gray-400 font-medium">
              <span>Lecciones</span>
              <span>Evaluaciones</span>
              <span>Progreso</span>
            </div>
          </div>

          <div className="space-y-3">
            {[
              {
                name: "Cirugía General",
                icon: Scissors,
                lessons: 45,
                evaluations: 12,
                progress: "89%",
                color: "text-red-600",
                bgColor: "bg-gradient-to-r from-red-50 to-red-100",
                hoverColor: "hover:from-red-100 hover:to-red-200"
              },
              {
                name: "Medicina Interna",
                icon: Stethoscope,
                lessons: 67,
                evaluations: 18,
                progress: "92%",
                color: "text-blue-600",
                bgColor: "bg-gradient-to-r from-blue-50 to-blue-100",
                hoverColor: "hover:from-blue-100 hover:to-blue-200"
              },
              {
                name: "Obstetricia y Ginecología",
                icon: Baby,
                lessons: 38,
                evaluations: 9,
                progress: "76%",
                color: "text-pink-600",
                bgColor: "bg-gradient-to-r from-pink-50 to-pink-100",
                hoverColor: "hover:from-pink-100 hover:to-pink-200",
                href: "/lecciones/ginecologia-obstetricia"
              },
              {
                name: "Pediatría",
                icon: Heart,
                lessons: 52,
                evaluations: 15,
                progress: "85%",
                color: "text-green-600",
                bgColor: "bg-gradient-to-r from-green-50 to-green-100",
                hoverColor: "hover:from-green-100 hover:to-green-200"
              },
              {
                name: "Salud Pública",
                icon: Users,
                lessons: 29,
                evaluations: 8,
                progress: "68%",
                color: "text-purple-600",
                bgColor: "bg-gradient-to-r from-purple-50 to-purple-100",
                hoverColor: "hover:from-purple-100 hover:to-purple-200"
              }
            ].map((specialty, index) => (
              <Link key={index} href={specialty.href || "#"} passHref>
                <div className={`${specialty.bgColor} ${specialty.hoverColor} rounded-2xl p-6 transition-all duration-300 cursor-pointer group`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-6">
                      <div className="w-14 h-14 bg-white rounded-2xl flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                        <specialty.icon className={`w-7 h-7 ${specialty.color}`} />
                      </div>
                      <div>
                        <span className="font-medium text-gray-800 text-lg">{specialty.name}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-16 text-sm">
                      <div className="text-center">
                        <div className="font-semibold text-gray-700 text-lg">{specialty.lessons}</div>
                        <div className="text-gray-500">lecciones</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold text-gray-700 text-lg">{specialty.evaluations}</div>
                        <div className="text-gray-500">evaluaciones</div>
                      </div>
                      <div className="text-center min-w-[80px]">
                        <div className={`font-semibold text-lg ${specialty.color}`}>{specialty.progress}</div>
                        <div className="text-gray-500">completado</div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>



        {/* Exam Simulators */}
        <div>
          <h2 className="text-2xl font-light text-gray-800 mb-8">Exámenes Simulacro</h2>

          <div className="space-y-6">
            <div className="bg-gradient-to-r from-emerald-50 to-emerald-100 p-8 rounded-2xl hover:from-emerald-100 hover:to-emerald-200 transition-all duration-300">
              <div className="flex justify-between items-start mb-4">
                <h4 className="font-medium text-emerald-800 text-xl">ENARM - Medicina Interna</h4>
                <span className="text-sm font-medium text-emerald-700 bg-white px-4 py-2 rounded-xl">Completado</span>
              </div>
              <p className="text-emerald-600 mb-6">Simulacro Nacional de Residencias</p>
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-4xl font-light text-emerald-800 mb-1">87/100</p>
                  <p className="text-sm text-emerald-600">Calificación estimada</p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-medium text-emerald-700">Percentil 92</p>
                  <p className="text-sm text-emerald-600">Posición nacional</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-8 rounded-2xl hover:from-blue-100 hover:to-blue-200 transition-all duration-300">
              <div className="flex justify-between items-start mb-4">
                <h4 className="font-medium text-blue-800 text-xl">ENARM - Cirugía General</h4>
                <span className="text-sm font-medium text-blue-700 bg-white px-4 py-2 rounded-xl">En Progreso</span>
              </div>
              <p className="text-blue-600 mb-6">Disponible hasta: 20 de Marzo</p>
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-4xl font-light text-blue-800 mb-1">45/100</p>
                  <p className="text-sm text-blue-600">Progreso actual</p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-medium text-blue-700">45% completado</p>
                  <div className="w-24 bg-white rounded-full h-3 mt-2">
                    <div className="bg-blue-600 h-3 rounded-full transition-all duration-500" style={{width: '45%'}}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
