package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"strings"
	"time"

	"cloud.google.com/go/storage"
	"google.golang.org/api/option"

	"turesibo-server/internal/config"
)

// ServiceAccountCredentials represents the structure of GCS service account credentials
type ServiceAccountCredentials struct {
	Type                    string `json:"type"`
	ProjectID               string `json:"project_id"`
	PrivateKeyID            string `json:"private_key_id"`
	PrivateKey              string `json:"private_key"`
	ClientEmail             string `json:"client_email"`
	ClientID                string `json:"client_id"`
	AuthURI                 string `json:"auth_uri"`
	TokenURI                string `json:"token_uri"`
	AuthProviderX509CertURL string `json:"auth_provider_x509_cert_url"`
	ClientX509CertURL       string `json:"client_x509_cert_url"`
}

// GCSService handles Google Cloud Storage operations
type GCSService struct {
	client     *storage.Client
	bucketName string
	bucket     *storage.BucketHandle
	config     *config.Settings
}

// NewGCSService creates a new GCS service instance
func NewGCSService(cfg *config.Settings) (*GCSService, error) {
	ctx := context.Background()

	// Process the private key to handle newlines correctly
	privateKey := cfg.GoogleCloudPrivateKey
	// Replace literal \n with actual newlines
	privateKey = strings.ReplaceAll(privateKey, "\\n", "\n")
	// Remove any extra quotes that might be present
	privateKey = strings.Trim(privateKey, "'\"")

	// Create credentials structure
	credentials := ServiceAccountCredentials{
		Type:                    "service_account",
		ProjectID:               cfg.GoogleCloudProjectID,
		PrivateKeyID:            cfg.GoogleCloudPrivateKeyID,
		PrivateKey:              privateKey,
		ClientEmail:             cfg.GoogleCloudClientEmail,
		ClientID:                cfg.GoogleCloudClientID,
		AuthURI:                 "https://accounts.google.com/o/oauth2/auth",
		TokenURI:                "https://oauth2.googleapis.com/token",
		AuthProviderX509CertURL: "https://www.googleapis.com/oauth2/v1/certs",
		ClientX509CertURL:       cfg.GoogleCloudClientCertURL,
	}

	// Marshal to JSON
	credentialsJSON, err := json.Marshal(credentials)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal GCS credentials: %w", err)
	}

	// Log for debugging (without sensitive data)
	log.Printf("Initializing GCS client for project: %s, bucket: %s", cfg.GoogleCloudProjectID, cfg.GoogleCloudStorageBucket)

	// Create client with credentials
	client, err := storage.NewClient(ctx, option.WithCredentialsJSON(credentialsJSON))
	if err != nil {
		// Log more details about the error for debugging
		log.Printf("GCS credentials JSON length: %d", len(credentialsJSON))
		if len(privateKey) > 50 {
			log.Printf("Private key starts with: %s", privateKey[:50])
		}
		return nil, fmt.Errorf("failed to create GCS client: %w", err)
	}

	bucketName := cfg.GoogleCloudStorageBucket
	bucket := client.Bucket(bucketName)

	// Check if bucket exists
	if _, err := bucket.Attrs(ctx); err != nil {
		return nil, fmt.Errorf("failed to access bucket %s: %w", bucketName, err)
	}

	log.Printf("[GCS] Successfully initialized GCS service for bucket: %s", bucketName)

	return &GCSService{
		client:     client,
		bucketName: bucketName,
		bucket:     bucket,
		config:     cfg,
	}, nil
}

// UploadFile uploads a file to Google Cloud Storage
func (g *GCSService) UploadFile(ctx context.Context, file multipart.File, header *multipart.FileHeader, destinationPath string, contentType string) (string, error) {
	log.Printf("[GCS] Uploading file to path: %s", destinationPath)
	log.Printf("[GCS] Content type: %s", contentType)

	// Create object handle
	obj := g.bucket.Object(destinationPath)

	// Create writer
	writer := obj.NewWriter(ctx)
	writer.ContentType = contentType

	// Copy file content
	if _, err := io.Copy(writer, file); err != nil {
		writer.Close()
		return "", fmt.Errorf("failed to upload file: %w", err)
	}

	// Close writer
	if err := writer.Close(); err != nil {
		return "", fmt.Errorf("failed to close writer: %w", err)
	}

	// Generate public URL
	publicURL := fmt.Sprintf("https://storage.googleapis.com/%s/%s", g.bucketName, destinationPath)
	log.Printf("[GCS] File uploaded successfully. Public URL: %s", publicURL)

	return publicURL, nil
}

// DeleteFile deletes a file from Google Cloud Storage
func (g *GCSService) DeleteFile(ctx context.Context, filePath string) error {
	log.Printf("[GCS] Attempting to delete file: %s", filePath)

	obj := g.bucket.Object(filePath)

	// Check if object exists
	if _, err := obj.Attrs(ctx); err != nil {
		if err == storage.ErrObjectNotExist {
			log.Printf("[GCS] File does not exist: %s", filePath)
			return nil // Not an error if file doesn't exist
		}
		return fmt.Errorf("failed to get object attributes: %w", err)
	}

	// Delete the object
	if err := obj.Delete(ctx); err != nil {
		return fmt.Errorf("failed to delete file: %w", err)
	}

	log.Printf("[GCS] File deleted successfully: %s", filePath)
	return nil
}

// GenerateSignedURL generates a signed URL for uploading a file
func (g *GCSService) GenerateSignedURL(ctx context.Context, objectName string, contentType string, method string) (string, error) {
	// Set expiration time
	expiration := time.Now().Add(time.Duration(g.config.GCSSignedURLExpiration) * time.Second)

	// Generate signed URL
	opts := &storage.SignedURLOptions{
		Scheme:      storage.SigningSchemeV4,
		Method:      method,
		Expires:     expiration,
		ContentType: contentType,
	}

	url, err := storage.SignedURL(g.bucketName, objectName, opts)
	if err != nil {
		return "", fmt.Errorf("failed to generate signed URL: %w", err)
	}

	return url, nil
}

// GetPublicURL returns the public URL for a file
func (g *GCSService) GetPublicURL(objectName string) string {
	return fmt.Sprintf("https://storage.googleapis.com/%s/%s", g.bucketName, objectName)
}

// Close closes the GCS client
func (g *GCSService) Close() error {
	return g.client.Close()
}
