# Database Configuration
POSTGRES_DATABASE="postgresql://postgres:postgres@localhost:5432/turesibo?sslmode=disable"
# For production, use a secure connection string like:
# POSTGRES_DATABASE="postgresql://username:password@host:port/database?sslmode=require"

# Google Cloud Storage Configuration
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_PRIVATE_KEY_ID=your-private-key-id
GOOGLE_CLOUD_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----\nyour-private-key-content\n-----END PRIVATE KEY-----\n
GOOGLE_CLOUD_CLIENT_EMAIL=<EMAIL>
GOOGLE_CLOUD_CLIENT_ID=your-client-id
GOOGLE_CLOUD_CLIENT_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
GOOGLE_CLOUD_STORAGE_BUCKET=your-bucket-name

# Server Configuration
PORT=8000
ENVIRONMENT=local
PROJECT_NAME=TuResiBo API
VERSION=0.1.0
API_V1_STR=/api/v1

# CORS Configuration
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# GCS Configuration
GCS_SIGNED_URL_EXPIRATION=300
