# TuResiBo Go Server Makefile

.PHONY: help build run test clean docker-build docker-run docker-compose-up docker-compose-down deps fmt vet

# Default target
help:
	@echo "Available commands:"
	@echo "  build              - Build the Go binary"
	@echo "  run                - Run the server locally"
	@echo "  test               - Run Go tests"
	@echo "  test-endpoints     - Test API endpoints (server must be running)"
	@echo "  clean              - Clean build artifacts"
	@echo "  docker-build       - Build Docker image"
	@echo "  docker-run         - Run Docker container"
	@echo "  docker-compose-up  - Start with Docker Compose"
	@echo "  docker-compose-down- Stop Docker Compose"
	@echo "  deps               - Download Go dependencies"
	@echo "  fmt                - Format Go code"
	@echo "  vet                - Run Go vet"
	@echo "  setup              - Setup development environment"

# Build the Go binary
build:
	@echo "Building Go binary..."
	go build -o bin/turesibo-server .

# Run the server locally
run:
	@echo "Starting server..."
	go run main.go

# Run Go tests
test:
	@echo "Running Go tests..."
	go test ./...

# Test API endpoints
test-endpoints:
	@echo "Testing API endpoints..."
	@if [ ! -f test_endpoints.sh ]; then echo "test_endpoints.sh not found"; exit 1; fi
	@chmod +x test_endpoints.sh
	./test_endpoints.sh

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf bin/
	go clean

# Build Docker image
docker-build:
	@echo "Building Docker image..."
	docker build -t turesibo-server:latest .

# Run Docker container
docker-run: docker-build
	@echo "Running Docker container..."
	docker run -p 8000:8000 --env-file .env turesibo-server:latest

# Start with Docker Compose
docker-compose-up:
	@echo "Starting with Docker Compose..."
	docker-compose up --build

# Stop Docker Compose
docker-compose-down:
	@echo "Stopping Docker Compose..."
	docker-compose down

# Download Go dependencies
deps:
	@echo "Downloading Go dependencies..."
	go mod download
	go mod tidy

# Format Go code
fmt:
	@echo "Formatting Go code..."
	go fmt ./...

# Run Go vet
vet:
	@echo "Running Go vet..."
	go vet ./...

# Setup development environment
setup:
	@echo "Setting up development environment..."
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from .env.example"; fi
	@echo "Please edit .env file with your configuration"
	go mod download
	@echo "Setup complete!"

# Development workflow
dev: fmt vet build
	@echo "Development build complete!"

# Production build
prod-build:
	@echo "Building for production..."
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-w -s' -o bin/turesibo-server .

# Install development tools
install-tools:
	@echo "Installing development tools..."
	go install golang.org/x/tools/cmd/goimports@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Run linter
lint:
	@echo "Running linter..."
	golangci-lint run

# Full check (format, vet, lint, test)
check: fmt vet lint test
	@echo "All checks passed!"
