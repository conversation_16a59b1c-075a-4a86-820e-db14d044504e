from typing import List, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..dependencies import get_db
from . import crud, schemas

router = APIRouter()


@router.post("/lesson_reviews/", response_model=schemas.LessonReview)
def create_lesson_review(
    lesson_review_in: schemas.LessonReviewCreate,
    db: Session = Depends(get_db)
) -> Any:
    """Create new lesson review."""
    created_review = crud.lesson_review.create(db, obj_in=lesson_review_in)
    return schemas.LessonReview.model_validate(created_review)


@router.get("/lesson_reviews/{lesson_review_id}", response_model=schemas.LessonReview)
def read_lesson_review(lesson_review_id: int, db: Session = Depends(get_db)):
    db_lesson_review = crud.get_lesson_review(db, lesson_review_id=lesson_review_id)
    if db_lesson_review is None:
        raise HTTPException(status_code=404, detail="Lesson review not found")
    return db_lesson_review


@router.get("/lesson_reviews/by_leccion/{leccion_id}", response_model=List[schemas.LessonReview])
def read_lesson_reviews_by_leccion(leccion_id: str, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)): 
    lesson_reviews = crud.get_lesson_reviews_by_leccion(db, leccion_id=leccion_id, skip=skip, limit=limit)
    return lesson_reviews


@router.get("/lesson_reviews/by_user/{user_id}", response_model=List[schemas.LessonReview])
def read_lesson_reviews_by_user(user_id: str, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)): 
    lesson_reviews = crud.get_lesson_reviews_by_user(db, user_id=user_id, skip=skip, limit=limit)
    return lesson_reviews