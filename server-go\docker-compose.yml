version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_DATABASE=${POSTGRES_DATABASE}
      - GOOGLE_CLOUD_PROJECT_ID=${GOOGLE_CLOUD_PROJECT_ID}
      - GOOGLE_CLOUD_PRIVATE_KEY_ID=${GOOGLE_CLOUD_PRIVATE_KEY_ID}
      - GOOGLE_CLOUD_PRIVATE_KEY=${GOOGLE_CLOUD_PRIVATE_KEY}
      - GOOGLE_CLOUD_CLIENT_EMAIL=${GOOGLE_CLOUD_CLIENT_EMAIL}
      - GOOGLE_CLOUD_CLIENT_ID=${GOOGLE_CLOUD_CLIENT_ID}
      - GOOGLE_CLOUD_CLIENT_CERT_URL=${GOOGLE_CLOUD_CLIENT_CERT_URL}
      - GOOG<PERSON>_CLOUD_STORAGE_BUCKET=${GOOGLE_CLOUD_STORAGE_BUCKET}
      - PORT=8000
      - ENVIRONMENT=production
      - PROJECT_NAME=TuResiBo API
      - VERSION=0.1.0
      - API_V1_STR=/api/v1
      - BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
      - GCS_SIGNED_URL_EXPIRATION=300
    depends_on:
      - postgres
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=turesibo
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
