package repository

import (
	"gorm.io/gorm"

	"turesibo-server/internal/dto"
	"turesibo-server/internal/models"
)

// PlaneadorRepository handles database operations for planeador
type PlaneadorRepository struct {
	db *gorm.DB
}

// NewPlaneadorRepository creates a new planeador repository
func NewPlaneadorRepository(db *gorm.DB) *PlaneadorRepository {
	return &PlaneadorRepository{db: db}
}

// CreateLessonReview creates a new lesson review
func (r *PlaneadorRepository) CreateLessonReview(data *dto.LessonReviewCreate) (*models.LessonReview, error) {
	lessonReview := &models.LessonReview{
		LeccionID: data.LeccionID,
		UserID:    data.UserID,
		Notes:     data.Notes,
	}

	if err := r.db.Create(lessonReview).Error; err != nil {
		return nil, err
	}

	return lessonReview, nil
}

// GetLessonReview gets a lesson review by ID
func (r *PlaneadorRepository) GetLessonReview(id uint) (*models.LessonReview, error) {
	var lessonReview models.LessonReview
	if err := r.db.First(&lessonReview, id).Error; err != nil {
		return nil, err
	}
	return &lessonReview, nil
}

// GetLessonReviewsByLeccion gets lesson reviews by leccion ID
func (r *PlaneadorRepository) GetLessonReviewsByLeccion(leccionID string, skip, limit int) ([]models.LessonReview, error) {
	var lessonReviews []models.LessonReview
	query := r.db.Where("leccion_id = ?", leccionID)
	
	if skip > 0 {
		query = query.Offset(skip)
	}
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if err := query.Find(&lessonReviews).Error; err != nil {
		return nil, err
	}
	
	return lessonReviews, nil
}

// GetLessonReviewsByUser gets lesson reviews by user ID
func (r *PlaneadorRepository) GetLessonReviewsByUser(userID string, skip, limit int) ([]models.LessonReview, error) {
	var lessonReviews []models.LessonReview
	query := r.db.Where("user_id = ?", userID)
	
	if skip > 0 {
		query = query.Offset(skip)
	}
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if err := query.Find(&lessonReviews).Error; err != nil {
		return nil, err
	}
	
	return lessonReviews, nil
}

// UpdateLessonReview updates a lesson review
func (r *PlaneadorRepository) UpdateLessonReview(id uint, data *dto.LessonReviewUpdate) (*models.LessonReview, error) {
	var lessonReview models.LessonReview
	if err := r.db.First(&lessonReview, id).Error; err != nil {
		return nil, err
	}

	// Update only non-nil fields
	updates := make(map[string]interface{})
	if data.LeccionID != nil {
		updates["leccion_id"] = *data.LeccionID
	}
	if data.UserID != nil {
		updates["user_id"] = *data.UserID
	}
	if data.Notes != nil {
		updates["notes"] = *data.Notes
	}

	if err := r.db.Model(&lessonReview).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &lessonReview, nil
}

// DeleteLessonReview deletes a lesson review
func (r *PlaneadorRepository) DeleteLessonReview(id uint) error {
	return r.db.Delete(&models.LessonReview{}, id).Error
}
