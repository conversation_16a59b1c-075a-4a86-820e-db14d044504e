# TuResiBo Server - Go Implementation

This is the Go implementation of the TuResiBo backend server, migrated from the original Python/FastAPI version.

## Features

- **Upload Content Management**: Handle videoclases, videos cortos, clinical notes, clinical cases, questionnaires, and flashcards
- **Google Cloud Storage Integration**: File upload and management
- **PostgreSQL Database**: Data persistence with GORM
- **RESTful API**: Clean REST endpoints with Gin framework
- **Planeador Module**: Lesson review management
- **CORS Support**: Cross-origin resource sharing
- **Docker Support**: Containerized deployment

## Tech Stack

- **Go 1.21+**
- **Gin Web Framework**
- **GORM** (PostgreSQL ORM)
- **Google Cloud Storage**
- **PostgreSQL**
- **Docker & Docker Compose**

## Environment Variables

Create a `.env` file with the following variables:

```env
# Database
POSTGRES_DATABASE="postgresql://postgres:postgres@localhost:5432/turesibo?sslmode=disable"

# Google Cloud Storage
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_PRIVATE_KEY_ID=your-private-key-id
GOOGLE_CLOUD_PRIVATE_KEY=your-private-key
GOOGLE_CLOUD_CLIENT_EMAIL=your-client-email
GOOGLE_CLOUD_CLIENT_ID=your-client-id
GOOGLE_CLOUD_CLIENT_CERT_URL=your-cert-url
GOOGLE_CLOUD_STORAGE_BUCKET=your-bucket-name

# Server Configuration
PORT=8000
ENVIRONMENT=local
PROJECT_NAME=TuResiBo API
VERSION=0.1.0
API_V1_STR=/api/v1

# CORS
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# GCS
GCS_SIGNED_URL_EXPIRATION=300
```

## Installation & Running

### Local Development

1. **Install Go 1.21+**

2. **Clone and setup**:
   ```bash
   cd server-go
   go mod download
   ```

3. **Setup environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the server**:
   ```bash
   go run main.go
   ```

The server will start on `http://localhost:8000`

### Docker Development

1. **Using Docker Compose** (includes PostgreSQL):
   ```bash
   docker-compose up --build
   ```

2. **Using Docker only**:
   ```bash
   docker build -t turesibo-server .
   docker run -p 8000:8000 --env-file .env turesibo-server
   ```

## API Endpoints

### Health Check
- `GET /health` - Health check endpoint

### Upload Content
- `POST /api/v1/upload-video` - Upload videoclase
- `GET /api/v1/videoclases` - List videoclases
- `PUT /api/v1/videos/:id` - Update videoclase
- `DELETE /api/v1/videos/:id` - Delete videoclase

- `POST /api/v1/upload-video-corto` - Upload video corto
- `GET /api/v1/videos-cortos` - List videos cortos
- `PUT /api/v1/videos-cortos/:id` - Update video corto
- `DELETE /api/v1/videos-cortos/:id` - Delete video corto

### Utility Endpoints
- `GET /api/v1/especialidades` - Get all especialidades
- `GET /api/v1/especialidades/:especialidad/sistemas` - Get sistemas by especialidad
- `GET /api/v1/especialidades/:especialidad/sistemas/:sistema/temas` - Get temas

### Planeador
- `POST /api/v1/planeador/lesson_reviews` - Create lesson review
- `GET /api/v1/planeador/lesson_reviews/:id` - Get lesson review
- `GET /api/v1/planeador/lesson_reviews/by_leccion/:leccion_id` - Get reviews by leccion
- `GET /api/v1/planeador/lesson_reviews/by_user/:user_id` - Get reviews by user
- `PUT /api/v1/planeador/lesson_reviews/:id` - Update lesson review
- `DELETE /api/v1/planeador/lesson_reviews/:id` - Delete lesson review

## Project Structure

```
server-go/
├── main.go                 # Application entry point
├── internal/
│   ├── config/            # Configuration management
│   ├── database/          # Database connection and migrations
│   ├── dto/               # Data Transfer Objects
│   ├── handlers/          # HTTP handlers
│   ├── middleware/        # HTTP middleware
│   ├── models/            # Database models
│   ├── repository/        # Data access layer
│   └── services/          # Business logic services
├── go.mod                 # Go module file
├── go.sum                 # Go dependencies
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Docker Compose configuration
└── README.md             # This file
```

## Migration from Python

This Go implementation maintains API compatibility with the original Python/FastAPI version:

- Same endpoint paths and HTTP methods
- Same request/response formats
- Same database schema
- Same environment variables
- Same Google Cloud Storage integration

## Development

### Adding New Endpoints

1. Define DTOs in `internal/dto/`
2. Create database models in `internal/models/`
3. Implement repository methods in `internal/repository/`
4. Create handlers in `internal/handlers/`
5. Register routes in the handler's `RegisterRoutes` method

### Database Migrations

GORM handles automatic migrations. Models are automatically migrated on startup.

## Production Deployment

1. **Build the Docker image**:
   ```bash
   docker build -t turesibo-server:latest .
   ```

2. **Deploy with proper environment variables**
3. **Ensure PostgreSQL database is available**
4. **Configure Google Cloud Storage credentials**

## Status

### Implemented ✅
- Project structure and configuration
- Database models and connections
- Google Cloud Storage service
- Videoclases upload/management
- Videos cortos upload/management
- Planeador lesson reviews
- Utility endpoints (especialidades, sistemas, temas)
- Docker configuration
- CORS and middleware

### Pending Implementation 🚧
- Clinical notes upload/management
- Clinical cases upload/management
- Questionnaires (cuestionarios) management
- Flashcards management
- JSON bulk upload endpoints
- Complete error handling and validation

The core functionality for video uploads and planeador is fully implemented and ready for testing.
