package models

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// VideoclasesData represents the videoclases_data table
type VideoclasesData struct {
	ID           uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Title        string         `json:"title" gorm:"size:255;not null"`
	Especialidad string         `json:"especialidad" gorm:"size:255;not null"`
	Sistema      string         `json:"sistema" gorm:"size:255;not null"`
	Tema         string         `json:"tema" gorm:"size:255;not null"`
	URL          *string        `json:"url" gorm:"size:512"`
	FilePath     *string        `json:"file_path" gorm:"size:512"`
	ThumbnailURL *string        `json:"thumbnail_url" gorm:"size:512"`
	Duration     *int           `json:"duration"`
	Description  *string        `json:"description" gorm:"type:text"`
	CreatedAt    time.Time      `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName specifies the table name for VideoclasesData
func (VideoclasesData) TableName() string {
	return "videoclases_data"
}

// VideosCortosData represents the videos_cortos_data table
type VideosCortosData struct {
	ID           uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Title        string         `json:"title" gorm:"size:255;not null"`
	Especialidad string         `json:"especialidad" gorm:"size:255;not null"`
	Sistema      string         `json:"sistema" gorm:"size:255;not null"`
	Tema         string         `json:"tema" gorm:"size:255;not null"`
	URL          *string        `json:"url" gorm:"size:512"`
	FilePath     *string        `json:"file_path" gorm:"size:512"`
	ThumbnailURL *string        `json:"thumbnail_url" gorm:"size:512"`
	Duration     *int           `json:"duration"`
	Description  *string        `json:"description" gorm:"type:text"`
	CreatedAt    time.Time      `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName specifies the table name for VideosCortosData
func (VideosCortosData) TableName() string {
	return "videos_cortos_data"
}

// NotasClinicasData represents the notas_clinicas_data table
type NotasClinicasData struct {
	ID           uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Title        string         `json:"title" gorm:"size:255;not null"`
	Especialidad string         `json:"especialidad" gorm:"size:255;not null"`
	Sistema      string         `json:"sistema" gorm:"size:255;not null"`
	Tema         string         `json:"tema" gorm:"size:255;not null"`
	Content      string         `json:"content" gorm:"type:text;not null"`
	Images       datatypes.JSON `json:"images" gorm:"type:jsonb"`
	CreatedAt    time.Time      `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName specifies the table name for NotasClinicasData
func (NotasClinicasData) TableName() string {
	return "notas_clinicas_data"
}

// CasosClinicosData represents the casos_clinicos_data table
type CasosClinicosData struct {
	ID           uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Title        string         `json:"title" gorm:"size:255;not null"`
	Especialidad string         `json:"especialidad" gorm:"size:255;not null"`
	Sistema      string         `json:"sistema" gorm:"size:255;not null"`
	Tema         string         `json:"tema" gorm:"size:255;not null"`
	Descripcion  string         `json:"descripcion" gorm:"type:text;not null"`
	Images       datatypes.JSON `json:"images" gorm:"type:jsonb"`
	Preguntas    datatypes.JSON `json:"preguntas" gorm:"type:jsonb;not null"`
	CreatedAt    time.Time      `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName specifies the table name for CasosClinicosData
func (CasosClinicosData) TableName() string {
	return "casos_clinicos_data"
}

// CuestionariosData represents the cuestionarios_data table
type CuestionariosData struct {
	ID           uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Title        string         `json:"title" gorm:"size:255;not null"`
	Especialidad string         `json:"especialidad" gorm:"size:255;not null"`
	Sistema      string         `json:"sistema" gorm:"size:255;not null"`
	Tema         string         `json:"tema" gorm:"size:255;not null"`
	Preguntas    datatypes.JSON `json:"preguntas" gorm:"type:jsonb;not null"`
	CreatedAt    time.Time      `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName specifies the table name for CuestionariosData
func (CuestionariosData) TableName() string {
	return "cuestionarios_data"
}

// FlashcardsData represents the flashcards_data table
type FlashcardsData struct {
	ID           uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Title        string         `json:"title" gorm:"size:255;not null"`
	Especialidad string         `json:"especialidad" gorm:"size:255;not null"`
	Sistema      string         `json:"sistema" gorm:"size:255;not null"`
	Tema         string         `json:"tema" gorm:"size:255;not null"`
	Pregunta     string         `json:"pregunta" gorm:"type:text;not null"`
	Respuesta    string         `json:"respuesta" gorm:"type:text;not null"`
	Etiquetas    datatypes.JSON `json:"etiquetas" gorm:"type:jsonb"`
	CreatedAt    time.Time      `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName specifies the table name for FlashcardsData
func (FlashcardsData) TableName() string {
	return "flashcards_data"
}

// RepasoData represents the repaso_datos table
type RepasoData struct {
	ID           uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Title        string         `json:"title" gorm:"size:255;not null"`
	Especialidad string         `json:"especialidad" gorm:"size:255;not null"`
	Sistema      string         `json:"sistema" gorm:"size:255;not null"`
	Tema         string         `json:"tema" gorm:"size:255;not null"`
	ImageURL     *string        `json:"image_url" gorm:"size:512"`
	FilePath     *string        `json:"file_path" gorm:"size:512"`
	Description  *string        `json:"description" gorm:"type:text"`
	CreatedAt    time.Time      `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName specifies the table name for RepasoData
func (RepasoData) TableName() string {
	return "repaso_datos"
}
