package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"turesibo-server/internal/dto"
	"turesibo-server/internal/middleware"
	"turesibo-server/internal/repository"
)

// PlaneadorHandler handles planeador operations
type PlaneadorHandler struct {
	repo *repository.PlaneadorRepository
}

// NewPlaneadorHandler creates a new planeador handler
func NewPlaneadorHandler(db *gorm.DB) *PlaneadorHandler {
	return &PlaneadorHandler{
		repo: repository.NewPlaneadorRepository(db),
	}
}

// RegisterRoutes registers all planeador routes
func (h *PlaneadorHandler) RegisterRoutes(router *gin.RouterGroup) {
	planeador := router.Group("/planeador")
	{
		planeador.POST("/lesson_reviews", h.CreateLessonReview)
		planeador.GET("/lesson_reviews/:id", h.GetLessonReview)
		planeador.GET("/lesson_reviews/by_leccion/:leccion_id", h.GetLessonReviewsByLeccion)
		planeador.GET("/lesson_reviews/by_user/:user_id", h.<PERSON>LessonReviewsByUser)
		planeador.PUT("/lesson_reviews/:id", h.UpdateLessonReview)
		planeador.DELETE("/lesson_reviews/:id", h.DeleteLessonReview)
	}
}

// CreateLessonReview handles creating a new lesson review
func (h *PlaneadorHandler) CreateLessonReview(c *gin.Context) {
	var req dto.LessonReviewCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	lessonReview, err := h.repo.CreateLessonReview(&req)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to create lesson review", err)
		return
	}

	lessonReviewOut := dto.LessonReviewOut{
		ID:        lessonReview.ID,
		LeccionID: lessonReview.LeccionID,
		UserID:    lessonReview.UserID,
		Notes:     lessonReview.Notes,
		CreatedAt: lessonReview.CreatedAt,
		UpdatedAt: lessonReview.UpdatedAt,
	}

	c.JSON(http.StatusCreated, lessonReviewOut)
}

// GetLessonReview handles getting a lesson review by ID
func (h *PlaneadorHandler) GetLessonReview(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid ID", err)
		return
	}

	lessonReview, err := h.repo.GetLessonReview(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			middleware.HandleError(c, http.StatusNotFound, "Lesson review not found", err)
		} else {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to get lesson review", err)
		}
		return
	}

	lessonReviewOut := dto.LessonReviewOut{
		ID:        lessonReview.ID,
		LeccionID: lessonReview.LeccionID,
		UserID:    lessonReview.UserID,
		Notes:     lessonReview.Notes,
		CreatedAt: lessonReview.CreatedAt,
		UpdatedAt: lessonReview.UpdatedAt,
	}

	c.JSON(http.StatusOK, lessonReviewOut)
}

// GetLessonReviewsByLeccion handles getting lesson reviews by leccion ID
func (h *PlaneadorHandler) GetLessonReviewsByLeccion(c *gin.Context) {
	leccionID := c.Param("leccion_id")
	if leccionID == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing leccion_id parameter", nil)
		return
	}

	// Parse query parameters
	skipStr := c.DefaultQuery("skip", "0")
	limitStr := c.DefaultQuery("limit", "100")

	skip, err := strconv.Atoi(skipStr)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid skip parameter", err)
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid limit parameter", err)
		return
	}

	lessonReviews, err := h.repo.GetLessonReviewsByLeccion(leccionID, skip, limit)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to get lesson reviews", err)
		return
	}

	// Convert to DTOs
	lessonReviewsOut := make([]dto.LessonReviewOut, len(lessonReviews))
	for i, lr := range lessonReviews {
		lessonReviewsOut[i] = dto.LessonReviewOut{
			ID:        lr.ID,
			LeccionID: lr.LeccionID,
			UserID:    lr.UserID,
			Notes:     lr.Notes,
			CreatedAt: lr.CreatedAt,
			UpdatedAt: lr.UpdatedAt,
		}
	}

	c.JSON(http.StatusOK, lessonReviewsOut)
}

// GetLessonReviewsByUser handles getting lesson reviews by user ID
func (h *PlaneadorHandler) GetLessonReviewsByUser(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing user_id parameter", nil)
		return
	}

	// Parse query parameters
	skipStr := c.DefaultQuery("skip", "0")
	limitStr := c.DefaultQuery("limit", "100")

	skip, err := strconv.Atoi(skipStr)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid skip parameter", err)
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid limit parameter", err)
		return
	}

	lessonReviews, err := h.repo.GetLessonReviewsByUser(userID, skip, limit)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to get lesson reviews", err)
		return
	}

	// Convert to DTOs
	lessonReviewsOut := make([]dto.LessonReviewOut, len(lessonReviews))
	for i, lr := range lessonReviews {
		lessonReviewsOut[i] = dto.LessonReviewOut{
			ID:        lr.ID,
			LeccionID: lr.LeccionID,
			UserID:    lr.UserID,
			Notes:     lr.Notes,
			CreatedAt: lr.CreatedAt,
			UpdatedAt: lr.UpdatedAt,
		}
	}

	c.JSON(http.StatusOK, lessonReviewsOut)
}

// UpdateLessonReview handles updating a lesson review
func (h *PlaneadorHandler) UpdateLessonReview(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid ID", err)
		return
	}

	var req dto.LessonReviewUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	lessonReview, err := h.repo.UpdateLessonReview(uint(id), &req)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			middleware.HandleError(c, http.StatusNotFound, "Lesson review not found", err)
		} else {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to update lesson review", err)
		}
		return
	}

	lessonReviewOut := dto.LessonReviewOut{
		ID:        lessonReview.ID,
		LeccionID: lessonReview.LeccionID,
		UserID:    lessonReview.UserID,
		Notes:     lessonReview.Notes,
		CreatedAt: lessonReview.CreatedAt,
		UpdatedAt: lessonReview.UpdatedAt,
	}

	c.JSON(http.StatusOK, lessonReviewOut)
}

// DeleteLessonReview handles deleting a lesson review
func (h *PlaneadorHandler) DeleteLessonReview(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid ID", err)
		return
	}

	if err := h.repo.DeleteLessonReview(uint(id)); err != nil {
		if err == gorm.ErrRecordNotFound {
			middleware.HandleError(c, http.StatusNotFound, "Lesson review not found", err)
		} else {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to delete lesson review", err)
		}
		return
	}

	response := map[string]interface{}{
		"success": true,
		"message": "Lesson review deleted successfully",
	}

	c.JSON(http.StatusOK, response)
}
