"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/lecciones/pediatria/page",{

/***/ "(app-pages-browser)/./app/lecciones/pediatria/page.tsx":
/*!******************************************!*\
  !*** ./app/lecciones/pediatria/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PediatriaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst especialidad = \"Pediatria\";\n// Helper function to get API base URL (same pattern as upload components)\nfunction getApiBaseUrl() {\n    var _process_env_NEXT_PUBLIC_API_BASE_URL;\n    const base = (_process_env_NEXT_PUBLIC_API_BASE_URL = \"http://localhost:8000/\") === null || _process_env_NEXT_PUBLIC_API_BASE_URL === void 0 ? void 0 : _process_env_NEXT_PUBLIC_API_BASE_URL.replace(/\\/$/, '');\n    return base || 'http://localhost:8000';\n}\nasync function fetchContent(contentType) {\n    try {\n        const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/contenido/\").concat(contentType, \"/?especialidad=\").concat(especialidad));\n        if (!response.ok) {\n            if (response.status === 404) {\n                // Return empty array if endpoint doesn't exist yet\n                return [];\n            }\n            throw new Error(\"Failed to fetch \".concat(contentType, \": \").concat(response.status, \" \").concat(response.statusText));\n        }\n        return response.json();\n    } catch (error) {\n        if (error instanceof TypeError && error.message.includes('fetch')) {\n            throw new Error(\"Network error while fetching \".concat(contentType, \". Make sure the server is running.\"));\n        }\n        throw error;\n    }\n}\nfunction ContentSection(param) {\n    let { title, data, basePath, onSpecialView } = param;\n    if (!data || data.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    onSpecialView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onSpecialView,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\",\n                        children: \"Ver en modo interactivo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"space-y-2\",\n                children: data.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"p-2 border rounded hover:bg-gray-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/lecciones/pediatria/\".concat(basePath, \"/\").concat(item.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: item.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                \" -\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        item.sistema,\n                                        \" / \",\n                                        item.tema\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    }, item.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n_c = ContentSection;\nfunction PediatriaPage() {\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        videoclases: null,\n        videos_cortos: null,\n        notas_clinicas: null,\n        casos_clinicos: null,\n        cuestionarios: null,\n        flashcards: null,\n        repaso: null\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewingCasosClinicosUI, setViewingCasosClinicosUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PediatriaPage.useEffect\": ()=>{\n            async function loadContent() {\n                try {\n                    const contentTypes = [\n                        \"videoclases\",\n                        \"videos-cortos\",\n                        \"notas-clinicas\",\n                        \"casos-clinicos\",\n                        \"cuestionarios\",\n                        \"flashcards\",\n                        \"repaso\"\n                    ];\n                    // Use Promise.allSettled to handle individual failures gracefully\n                    const results = await Promise.allSettled(contentTypes.map({\n                        \"PediatriaPage.useEffect.loadContent\": (type)=>fetchContent(type)\n                    }[\"PediatriaPage.useEffect.loadContent\"]));\n                    const newContent = {};\n                    const errors = [];\n                    contentTypes.forEach({\n                        \"PediatriaPage.useEffect.loadContent\": (type, index)=>{\n                            const result = results[index];\n                            if (result.status === 'fulfilled') {\n                                newContent[type.replace(/-/g, '_')] = result.value;\n                            } else {\n                                newContent[type.replace(/-/g, '_')] = [];\n                                errors.push(\"\".concat(type, \": \").concat(result.reason.message));\n                            }\n                        }\n                    }[\"PediatriaPage.useEffect.loadContent\"]);\n                    setContent(newContent);\n                    // Only show error if all requests failed\n                    if (errors.length === contentTypes.length) {\n                        setError(\"Failed to load content: \".concat(errors.join(', ')));\n                    } else if (errors.length > 0) {\n                        console.warn('Some content types failed to load:', errors);\n                    }\n                } catch (err) {\n                    if (err instanceof Error) {\n                        setError(err.message);\n                    } else {\n                        setError(\"An unknown error occurred\");\n                    }\n                } finally{\n                    setLoading(false);\n                }\n            }\n            loadContent();\n        }\n    }[\"PediatriaPage.useEffect\"], []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: \"Loading content...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n            lineNumber: 196,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: [\n                \"Error: \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n            lineNumber: 200,\n            columnNumber: 12\n        }, this);\n    }\n    // Check if any content is available\n    const hasAnyContent = Object.values(content).some((data)=>data && data.length > 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-6\",\n                children: \"Lecciones de Pediatr\\xeda\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            !hasAnyContent && !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-lg\",\n                        children: \"No hay contenido disponible para Pediatr\\xeda en este momento.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-sm mt-2\",\n                        children: \"El contenido se mostrar\\xe1 aqu\\xed una vez que sea subido al sistema.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Videoclases\",\n                data: content.videoclases,\n                basePath: \"videoclases\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Videos Cortos\",\n                data: content.videos_cortos,\n                basePath: \"videos-cortos\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Notas Cl\\xednicas\",\n                data: content.notas_clinicas,\n                basePath: \"notas-clinicas\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Casos Cl\\xednicos\",\n                data: content.casos_clinicos,\n                basePath: \"casos-clinicos\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Cuestionarios\",\n                data: content.cuestionarios,\n                basePath: \"cuestionarios\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Flashcards\",\n                data: content.flashcards,\n                basePath: \"flashcards\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSection, {\n                title: \"Repaso\",\n                data: content.repaso,\n                basePath: \"repaso\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\lecciones\\\\pediatria\\\\page.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_s(PediatriaPage, \"/h9WASC653+hBcUyTQrZWKrXoZ8=\");\n_c1 = PediatriaPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ContentSection\");\n$RefreshReg$(_c1, \"PediatriaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/lecciones/pediatria/page.tsx\n"));

/***/ })

});