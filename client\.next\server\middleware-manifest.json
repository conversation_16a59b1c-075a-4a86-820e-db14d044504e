{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vpuyGpnRGjkFou8C30vySVISi+U2geFoS/CwcDPvqwM=", "__NEXT_PREVIEW_MODE_ID": "f0089b5ec3e9e80a054bbc6e9267b58b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "387ea31fc3170e3f2dabfef9b7b90be450ff11b3af7e5a49a905f09edfaf264f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "93029230bb37b68fa24de7ea38de2be1d36238bb160c08f57d386597aead7190"}}}, "functions": {}, "sortedMiddleware": ["/"]}