package models

import (
	"time"

	"gorm.io/gorm"
)

// LessonReview represents the planeador_lesson_reviews table
type LessonReview struct {
	ID        uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	LeccionID string         `json:"leccion_id" gorm:"index;not null"`
	UserID    string         `json:"user_id" gorm:"index;not null"`
	Notes     *string        `json:"notes"`
	CreatedAt time.Time      `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName specifies the table name for LessonReview
func (LessonReview) TableName() string {
	return "planeador_lesson_reviews"
}
