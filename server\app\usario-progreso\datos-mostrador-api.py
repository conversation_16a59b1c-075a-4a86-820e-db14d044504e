from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..dependencies.db import get_db
from ..upload_content import crud, models, schemas

router = APIRouter()


def get_filtered_query(
    db: Session,
    model,
    especialidad: Optional[str] = None,
    sistema: Optional[str] = None,
    tema: Optional[str] = None,
):
    query = db.query(model)
    if especialidad:
        query = query.filter(model.especialidad == especialidad)
    if sistema:
        query = query.filter(model.sistema == sistema)
    if tema:
        query = query.filter(model.tema == tema)
    return query.order_by(model.created_at.desc()).all()


@router.get("/videoclases/", response_model=List[schemas.VideoclaseOut])
def read_videoclases(
    especialidad: Optional[str] = None,
    sistema: Optional[str] = None,
    tema: Optional[str] = None,
    db: Session = Depends(get_db),
):
    """
    Retrieve videoclases with optional filters.
    """
    videoclases = get_filtered_query(
        db, models.VideoclasesData, especialidad, sistema, tema
    )
    return videoclases


@router.get("/videos-cortos/", response_model=List[schemas.VideoCortoOut])
def read_videos_cortos(
    especialidad: Optional[str] = None,
    sistema: Optional[str] = None,
    tema: Optional[str] = None,
    db: Session = Depends(get_db),
):
    """
    Retrieve videos cortos with optional filters.
    """
    videos_cortos = get_filtered_query(
        db, models.VideosCortosData, especialidad, sistema, tema
    )
    return videos_cortos


@router.get("/notas-clinicas/", response_model=List[schemas.NotaClinicaOut])
def read_notas_clinicas(
    especialidad: Optional[str] = None,
    sistema: Optional[str] = None,
    tema: Optional[str] = None,
    db: Session = Depends(get_db),
):
    """
    Retrieve notas clinicas with optional filters.
    """
    notas_clinicas = get_filtered_query(
        db, models.NotasClinicasData, especialidad, sistema, tema
    )
    return notas_clinicas


@router.get("/casos-clinicos/", response_model=List[schemas.CasoClinicoOut])
def read_casos_clinicos(
    especialidad: Optional[str] = None,
    sistema: Optional[str] = None,
    tema: Optional[str] = None,
    db: Session = Depends(get_db),
):
    """
    Retrieve casos clinicos with optional filters.
    """
    casos_clinicos = get_filtered_query(
        db, models.CasosClinicosData, especialidad, sistema, tema
    )
    return casos_clinicos


@router.get("/cuestionarios/", response_model=List[schemas.CuestionarioOut])
def read_cuestionarios(
    especialidad: Optional[str] = None,
    sistema: Optional[str] = None,
    tema: Optional[str] = None,
    db: Session = Depends(get_db),
):
    """
    Retrieve cuestionarios with optional filters.
    """
    cuestionarios = get_filtered_query(
        db, models.CuestionariosData, especialidad, sistema, tema
    )
    return cuestionarios


@router.get("/flashcards/", response_model=List[schemas.FlashcardOut])
def read_flashcards(
    especialidad: Optional[str] = None,
    sistema: Optional[str] = None,
    tema: Optional[str] = None,
    db: Session = Depends(get_db),
):
    """
    Retrieve flashcards with optional filters.
    """
    flashcards = get_filtered_query(
        db, models.FlashcardsData, especialidad, sistema, tema
    )
    return flashcards


@router.get("/repaso/", response_model=List[schemas.RepasoOut])
def read_repaso(
    especialidad: Optional[str] = None,
    sistema: Optional[str] = None,
    tema: Optional[str] = None,
    db: Session = Depends(get_db),
):
    """
    Retrieve repaso content with optional filters.
    """
    repaso_content = get_filtered_query(
        db, models.RepasoData, especialidad, sistema, tema
    )
    return repaso_content


@router.get("/especialidades/", response_model=List[str])
def read_especialidades(db: Session = Depends(get_db)):
    """
    Retrieve all unique especialidades.
    """
    return crud.get_unique_especialidades(db)


@router.get("/sistemas/", response_model=List[str])
def read_sistemas(especialidad: str, db: Session = Depends(get_db)):
    """
    Retrieve all unique sistemas for a given especialidad.
    """
    return crud.get_sistemas_by_especialidad(db, especialidad)


@router.get("/temas/", response_model=List[str])
def read_temas(especialidad: str, sistema: str, db: Session = Depends(get_db)):
    """
    Retrieve all unique temas for a given especialidad and sistema.
    """
    return crud.get_temas_by_especialidad_sistema(db, especialidad, sistema)
