package dto

import "time"

// LessonReviewBase contains the base fields for lesson reviews
type LessonReviewBase struct {
	LeccionID string  `json:"leccion_id" binding:"required"`
	UserID    string  `json:"user_id" binding:"required"`
	Notes     *string `json:"notes,omitempty"`
}

// LessonReviewCreate is used for creating new lesson reviews
type LessonReviewCreate struct {
	LessonReviewBase
}

// LessonReviewUpdate is used for updating lesson reviews
type LessonReviewUpdate struct {
	LeccionID *string `json:"leccion_id,omitempty"`
	UserID    *string `json:"user_id,omitempty"`
	Notes     *string `json:"notes,omitempty"`
}

// LessonReviewOut is the output DTO for lesson reviews
type LessonReviewOut struct {
	ID        uint      `json:"id"`
	LeccionID string    `json:"leccion_id"`
	UserID    string    `json:"user_id"`
	Notes     *string   `json:"notes,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
