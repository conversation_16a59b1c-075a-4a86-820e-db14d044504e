from fastapi import APIRouter

from app.upload_content.upload_api import router as upload_router
from app.planeador.api import router as planeador_router
from app.usuario_progreso.datos_mostrador_api import router as datos_mostrador_router

api_router = APIRouter()
api_router.include_router(upload_router, prefix="/upload-content", tags=["upload-content"])
api_router.include_router(planeador_router, prefix="/planeador", tags=["planeador"])
api_router.include_router(datos_mostrador_router, prefix="/contenido", tags=["contenido"])
