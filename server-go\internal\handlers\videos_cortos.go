package handlers

import (
	"context"
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"turesibo-server/internal/dto"
	"turesibo-server/internal/middleware"
)

// UploadVideoCorto handles video corto upload
func (h *UploadHandler) UploadVideoCorto(c *gin.Context) {
	ctx := context.Background()

	// Parse form data
	especialidad := c.PostForm("especialidad")
	sistema := c.PostForm("sistema")
	tema := c.PostForm("tema")
	titulo := c.PostForm("titulo")
	description := c.PostForm("description")

	if especialidad == "" || sistema == "" || tema == "" || titulo == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing required fields", nil)
		return
	}

	var videoURL, videoFilePath, thumbnailURL *string
	var desc *string
	if description != "" {
		desc = &description
	}

	// Handle video file upload
	videoFile, videoHeader, err := c.Request.FormFile("file")
	if err == nil {
		defer videoFile.Close()

		// Validate video file
		if !h.validateVideoFile(videoHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid video file type", nil)
			return
		}

		// Generate file path
		ext := strings.ToLower(filepath.Ext(videoHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("videos-cortos/%s/%s/%s/%d%s", especialidad, sistema, tema, timestamp, ext)
		contentType := fmt.Sprintf("video/%s", strings.TrimPrefix(ext, "."))

		// Upload to GCS
		url, err := h.gcsService.UploadFile(ctx, videoFile, videoHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload video", err)
			return
		}

		videoURL = &url
		videoFilePath = &filePath
	}

	// Handle thumbnail upload
	thumbnailFile, thumbnailHeader, err := c.Request.FormFile("thumbnail")
	if err == nil {
		defer thumbnailFile.Close()

		// Validate thumbnail file
		if !h.validateImageFile(thumbnailHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid thumbnail file type", nil)
			return
		}

		// Generate file path
		ext := strings.ToLower(filepath.Ext(thumbnailHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("thumbnails-cortos/%s/%s/%s/%d%s", especialidad, sistema, tema, timestamp, ext)
		contentType := fmt.Sprintf("image/%s", strings.TrimPrefix(ext, "."))

		// Upload to GCS
		url, err := h.gcsService.UploadFile(ctx, thumbnailFile, thumbnailHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload thumbnail", err)
			return
		}

		thumbnailURL = &url
	}

	// Create video corto if video was uploaded
	var videoCorto *dto.VideoCortoOut
	if videoURL != nil {
		createData := &dto.VideoCortoCreate{
			ContentBase: dto.ContentBase{
				Title:        titulo,
				Especialidad: especialidad,
				Sistema:      sistema,
				Tema:         tema,
				Description:  desc,
			},
			URL:          videoURL,
			FilePath:     videoFilePath,
			ThumbnailURL: thumbnailURL,
		}

		createdVideoCorto, err := h.repo.CreateVideoCorto(createData)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to create video corto", err)
			return
		}

		videoCorto = &dto.VideoCortoOut{
			ID:           createdVideoCorto.ID,
			Title:        createdVideoCorto.Title,
			Especialidad: createdVideoCorto.Especialidad,
			Sistema:      createdVideoCorto.Sistema,
			Tema:         createdVideoCorto.Tema,
			URL:          createdVideoCorto.URL,
			FilePath:     createdVideoCorto.FilePath,
			ThumbnailURL: createdVideoCorto.ThumbnailURL,
			Duration:     createdVideoCorto.Duration,
			Description:  createdVideoCorto.Description,
			CreatedAt:    createdVideoCorto.CreatedAt,
		}
	}

	response := map[string]interface{}{
		"success": true,
		"message": "Video corto subido exitosamente",
	}

	if videoCorto != nil {
		response["video_corto"] = videoCorto
	}

	c.JSON(http.StatusOK, response)
}

// ListVideosCortos handles listing all videos cortos
func (h *UploadHandler) ListVideosCortos(c *gin.Context) {
	videosCortos, err := h.repo.ListVideosCortos()
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to list videos cortos", err)
		return
	}

	// Convert to DTOs
	videosCortosOut := make([]dto.VideoCortoOut, len(videosCortos))
	for i, v := range videosCortos {
		videosCortosOut[i] = dto.VideoCortoOut{
			ID:           v.ID,
			Title:        v.Title,
			Especialidad: v.Especialidad,
			Sistema:      v.Sistema,
			Tema:         v.Tema,
			URL:          v.URL,
			FilePath:     v.FilePath,
			ThumbnailURL: v.ThumbnailURL,
			Duration:     v.Duration,
			Description:  v.Description,
			CreatedAt:    v.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, videosCortosOut)
}

// UpdateVideoCorto handles updating a video corto
func (h *UploadHandler) UpdateVideoCorto(c *gin.Context) {
	ctx := context.Background()

	// Get ID from URL
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid ID", err)
		return
	}

	// Get existing video corto
	existingVideoCorto, err := h.repo.GetVideoCorto(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			middleware.HandleError(c, http.StatusNotFound, "Video corto not found", err)
		} else {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to get video corto", err)
		}
		return
	}

	// Parse form data
	especialidad := c.PostForm("especialidad")
	sistema := c.PostForm("sistema")
	tema := c.PostForm("tema")
	titulo := c.PostForm("titulo")
	description := c.PostForm("description")

	if especialidad == "" || sistema == "" || tema == "" || titulo == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing required fields", nil)
		return
	}

	var desc *string
	if description != "" {
		desc = &description
	}

	updateData := &dto.VideoCortoUpdate{
		Title:        &titulo,
		Especialidad: &especialidad,
		Sistema:      &sistema,
		Tema:         &tema,
		Description:  desc,
		URL:          existingVideoCorto.URL,
		FilePath:     existingVideoCorto.FilePath,
		ThumbnailURL: existingVideoCorto.ThumbnailURL,
	}

	// Handle video file update
	videoFile, videoHeader, err := c.Request.FormFile("file")
	if err == nil {
		defer videoFile.Close()

		// Delete old video file if exists
		if existingVideoCorto.FilePath != nil {
			if err := h.gcsService.DeleteFile(ctx, *existingVideoCorto.FilePath); err != nil {
				fmt.Printf("Error deleting old video file: %v\n", err)
			}
		}

		// Validate and upload new video
		if !h.validateVideoFile(videoHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid video file type", nil)
			return
		}

		ext := strings.ToLower(filepath.Ext(videoHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("videos-cortos/%s/%s/%s/%d%s", especialidad, sistema, tema, timestamp, ext)
		contentType := fmt.Sprintf("video/%s", strings.TrimPrefix(ext, "."))

		url, err := h.gcsService.UploadFile(ctx, videoFile, videoHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload video", err)
			return
		}

		updateData.URL = &url
		updateData.FilePath = &filePath
	}

	// Handle thumbnail update
	thumbnailFile, thumbnailHeader, err := c.Request.FormFile("thumbnail")
	if err == nil {
		defer thumbnailFile.Close()

		// Validate and upload new thumbnail
		if !h.validateImageFile(thumbnailHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid thumbnail file type", nil)
			return
		}

		ext := strings.ToLower(filepath.Ext(thumbnailHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("thumbnails-cortos/%s/%s/%s/%d%s", especialidad, sistema, tema, timestamp, ext)
		contentType := fmt.Sprintf("image/%s", strings.TrimPrefix(ext, "."))

		url, err := h.gcsService.UploadFile(ctx, thumbnailFile, thumbnailHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload thumbnail", err)
			return
		}

		updateData.ThumbnailURL = &url
	}

	// Update video corto
	updatedVideoCorto, err := h.repo.UpdateVideoCorto(uint(id), updateData)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to update video corto", err)
		return
	}

	videoCortoOut := dto.VideoCortoOut{
		ID:           updatedVideoCorto.ID,
		Title:        updatedVideoCorto.Title,
		Especialidad: updatedVideoCorto.Especialidad,
		Sistema:      updatedVideoCorto.Sistema,
		Tema:         updatedVideoCorto.Tema,
		URL:          updatedVideoCorto.URL,
		FilePath:     updatedVideoCorto.FilePath,
		ThumbnailURL: updatedVideoCorto.ThumbnailURL,
		Duration:     updatedVideoCorto.Duration,
		Description:  updatedVideoCorto.Description,
		CreatedAt:    updatedVideoCorto.CreatedAt,
	}

	response := map[string]interface{}{
		"success":     true,
		"message":     "Video corto actualizado exitosamente",
		"video_corto": videoCortoOut,
	}

	c.JSON(http.StatusOK, response)
}

// DeleteVideoCorto handles deleting a video corto
func (h *UploadHandler) DeleteVideoCorto(c *gin.Context) {
	ctx := context.Background()

	// Get ID from URL
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid ID", err)
		return
	}

	// Get existing video corto to delete associated files
	existingVideoCorto, err := h.repo.GetVideoCorto(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			middleware.HandleError(c, http.StatusNotFound, "Video corto not found", err)
		} else {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to get video corto", err)
		}
		return
	}

	// Delete associated files from GCS
	if existingVideoCorto.FilePath != nil {
		if err := h.gcsService.DeleteFile(ctx, *existingVideoCorto.FilePath); err != nil {
			fmt.Printf("Error deleting video file: %v\n", err)
		}
	}

	// Delete video corto from database
	if err := h.repo.DeleteVideoCorto(uint(id)); err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to delete video corto", err)
		return
	}

	response := map[string]interface{}{
		"success": true,
		"message": "Video corto eliminado exitosamente",
	}

	c.JSON(http.StatusOK, response)
}
