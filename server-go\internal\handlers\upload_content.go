package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"turesibo-server/internal/dto"
	"turesibo-server/internal/middleware"
	"turesibo-server/internal/repository"
	"turesibo-server/internal/services"
)

// UploadHandler handles upload content operations
type UploadHandler struct {
	repo       *repository.UploadContentRepository
	gcsService *services.GCSService
}

// NewUploadHandler creates a new upload handler
func NewUploadHandler(db *gorm.DB, gcsService *services.GCSService) *UploadHandler {
	return &UploadHandler{
		repo:       repository.NewUploadContentRepository(db),
		gcsService: gcsService,
	}
}

// RegisterRoutes registers all upload content routes
func (h *UploadHandler) RegisterRoutes(router *gin.RouterGroup) {
	// Videoclases routes
	router.POST("/upload-video", h.UploadVideo)
	router.GET("/videoclases", h.ListVideoclases)
	router.PUT("/videos/:id", h.UpdateVideoclase)
	router.DELETE("/videos/:id", h.DeleteVideoclase)

	// Videos cortos routes
	router.POST("/upload-video-corto", h.UploadVideoCorto)
	router.GET("/videos-cortos", h.ListVideosCortos)
	router.PUT("/videos-cortos/:id", h.UpdateVideoCorto)
	router.DELETE("/videos-cortos/:id", h.DeleteVideoCorto)

	// Clinical notes routes
	router.POST("/clinical-notes", h.UploadClinicalNotes)
	router.GET("/clinical-notes", h.ListClinicalNotes)
	router.GET("/clinical-notes/:id", h.GetClinicalNote)

	// Clinical cases routes
	router.POST("/clinical-cases", h.UploadClinicalCase)
	router.GET("/clinical-cases", h.ListClinicalCases)
	router.GET("/clinical-cases/:id", h.GetClinicalCase)
	router.POST("/clinical-cases/json", h.UploadClinicalCasesJSON)
	router.PUT("/clinical-cases/:id", h.UpdateClinicalCase)
	router.DELETE("/clinical-cases/:id", h.DeleteClinicalCase)

	// Cuestionarios routes
	router.POST("/cuestionarios", h.CreateCuestionario)
	router.GET("/cuestionarios", h.ListCuestionarios)
	router.GET("/cuestionarios/:id", h.GetCuestionario)
	router.POST("/cuestionarios/json", h.UploadCuestionariosJSON)
	router.PUT("/cuestionarios/:id", h.UpdateCuestionario)
	router.DELETE("/cuestionarios/:id", h.DeleteCuestionario)

	// Flashcards routes
	router.POST("/flashcards", h.CreateFlashcard)
	router.GET("/flashcards", h.ListFlashcards)
	router.GET("/flashcards/:id", h.GetFlashcard)
	router.POST("/flashcards/json", h.UploadFlashcardsJSON)
	router.PUT("/flashcards/:id", h.UpdateFlashcard)
	router.DELETE("/flashcards/:id", h.DeleteFlashcard)

	// Utility routes
	router.GET("/especialidades", h.GetEspecialidades)
	router.GET("/especialidades/:especialidad/sistemas", h.GetSistemasByEspecialidad)
	router.GET("/especialidades/:especialidad/sistemas/:sistema/temas", h.GetTemasByEspecialidadSistema)
}

// Helper function to validate file extensions
func (h *UploadHandler) validateVideoFile(filename string) bool {
	allowedExtensions := map[string]bool{
		".mp4":  true,
		".webm": true,
		".mov":  true,
		".avi":  true,
		".mkv":  true,
	}
	ext := strings.ToLower(filepath.Ext(filename))
	return allowedExtensions[ext]
}

func (h *UploadHandler) validateImageFile(filename string) bool {
	allowedExtensions := map[string]bool{
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".webp": true,
		".gif":  true,
	}
	ext := strings.ToLower(filepath.Ext(filename))
	return allowedExtensions[ext]
}

// UploadVideo handles video upload for videoclases
func (h *UploadHandler) UploadVideo(c *gin.Context) {
	ctx := context.Background()

	// Parse form data
	especialidad := c.PostForm("especialidad")
	sistema := c.PostForm("sistema")
	tema := c.PostForm("tema")
	titulo := c.PostForm("titulo")
	quiz := c.PostForm("quiz")

	if especialidad == "" || sistema == "" || tema == "" || titulo == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing required fields", nil)
		return
	}

	var videoURL, videoFilePath, thumbnailURL *string

	// Handle video file upload
	videoFile, videoHeader, err := c.Request.FormFile("file")
	if err == nil {
		defer videoFile.Close()

		// Validate video file
		if !h.validateVideoFile(videoHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid video file type", nil)
			return
		}

		// Generate file path
		ext := strings.ToLower(filepath.Ext(videoHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("videoclases/%s/%s/%d%s", especialidad, tema, timestamp, ext)
		contentType := fmt.Sprintf("video/%s", strings.TrimPrefix(ext, "."))

		// Upload to GCS
		url, err := h.gcsService.UploadFile(ctx, videoFile, videoHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload video", err)
			return
		}

		videoURL = &url
		videoFilePath = &filePath
	}

	// Handle thumbnail upload
	thumbnailFile, thumbnailHeader, err := c.Request.FormFile("thumbnail")
	if err == nil {
		defer thumbnailFile.Close()

		// Validate thumbnail file
		if !h.validateImageFile(thumbnailHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid thumbnail file type", nil)
			return
		}

		// Generate file path
		ext := strings.ToLower(filepath.Ext(thumbnailHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("thumbnails/%s/%s/%d%s", especialidad, tema, timestamp, ext)
		contentType := fmt.Sprintf("image/%s", strings.TrimPrefix(ext, "."))

		// Upload to GCS
		url, err := h.gcsService.UploadFile(ctx, thumbnailFile, thumbnailHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload thumbnail", err)
			return
		}

		thumbnailURL = &url
	}

	// Create videoclase if video was uploaded
	var videoclase *dto.VideoclaseOut
	if videoURL != nil {
		createData := &dto.VideoclaseCreate{
			ContentBase: dto.ContentBase{
				Title:        titulo,
				Especialidad: especialidad,
				Sistema:      sistema,
				Tema:         tema,
			},
			URL:          videoURL,
			FilePath:     videoFilePath,
			ThumbnailURL: thumbnailURL,
		}

		createdVideoclase, err := h.repo.CreateVideoclase(createData)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to create videoclase", err)
			return
		}

		videoclase = &dto.VideoclaseOut{
			ID:           createdVideoclase.ID,
			Title:        createdVideoclase.Title,
			Especialidad: createdVideoclase.Especialidad,
			Sistema:      createdVideoclase.Sistema,
			Tema:         createdVideoclase.Tema,
			URL:          createdVideoclase.URL,
			FilePath:     createdVideoclase.FilePath,
			ThumbnailURL: createdVideoclase.ThumbnailURL,
			Duration:     createdVideoclase.Duration,
			Description:  createdVideoclase.Description,
			CreatedAt:    createdVideoclase.CreatedAt,
		}
	}

	// Handle quiz creation if provided
	if quiz != "" {
		var quizData map[string]interface{}
		if err := json.Unmarshal([]byte(quiz), &quizData); err != nil {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid quiz JSON format", err)
			return
		}

		// Convert quiz data to cuestionario format
		preguntas, ok := quizData["preguntas"].([]interface{})
		if !ok {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid quiz format: missing preguntas", nil)
			return
		}

		preguntasData := make([]map[string]interface{}, len(preguntas))
		for i, p := range preguntas {
			pregunta, ok := p.(map[string]interface{})
			if !ok {
				middleware.HandleError(c, http.StatusBadRequest, "Invalid question format", nil)
				return
			}
			preguntasData[i] = pregunta
		}

		cuestionarioData := &dto.CuestionarioCreate{
			ContentBase: dto.ContentBase{
				Title:        fmt.Sprintf("Cuestionario - %s", titulo),
				Especialidad: especialidad,
				Sistema:      sistema,
				Tema:         tema,
			},
			Preguntas: preguntasData,
		}

		_, err := h.repo.CreateCuestionario(cuestionarioData)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to create quiz", err)
			return
		}
	}

	response := map[string]interface{}{
		"success": true,
		"message": "Videoclase subida exitosamente",
	}

	if videoclase != nil {
		response["videoclase"] = videoclase
	}

	c.JSON(http.StatusOK, response)
}

// ListVideoclases handles listing all videoclases
func (h *UploadHandler) ListVideoclases(c *gin.Context) {
	videoclases, err := h.repo.ListVideoclases()
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to list videoclases", err)
		return
	}

	// Convert to DTOs
	videoclasesOut := make([]dto.VideoclaseOut, len(videoclases))
	for i, v := range videoclases {
		videoclasesOut[i] = dto.VideoclaseOut{
			ID:           v.ID,
			Title:        v.Title,
			Especialidad: v.Especialidad,
			Sistema:      v.Sistema,
			Tema:         v.Tema,
			URL:          v.URL,
			FilePath:     v.FilePath,
			ThumbnailURL: v.ThumbnailURL,
			Duration:     v.Duration,
			Description:  v.Description,
			CreatedAt:    v.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, videoclasesOut)
}

// UpdateVideoclase handles updating a videoclase
func (h *UploadHandler) UpdateVideoclase(c *gin.Context) {
	ctx := context.Background()

	// Get ID from URL
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid ID", err)
		return
	}

	// Get existing videoclase
	existingVideoclase, err := h.repo.GetVideoclase(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			middleware.HandleError(c, http.StatusNotFound, "Videoclase not found", err)
		} else {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to get videoclase", err)
		}
		return
	}

	// Parse form data
	especialidad := c.PostForm("especialidad")
	sistema := c.PostForm("sistema")
	tema := c.PostForm("tema")
	titulo := c.PostForm("titulo")

	if especialidad == "" || sistema == "" || tema == "" || titulo == "" {
		middleware.HandleError(c, http.StatusBadRequest, "Missing required fields", nil)
		return
	}

	updateData := &dto.VideoclaseUpdate{
		Title:        &titulo,
		Especialidad: &especialidad,
		Sistema:      &sistema,
		Tema:         &tema,
		URL:          existingVideoclase.URL,
		FilePath:     existingVideoclase.FilePath,
		ThumbnailURL: existingVideoclase.ThumbnailURL,
	}

	// Handle video file update
	videoFile, videoHeader, err := c.Request.FormFile("file")
	if err == nil {
		defer videoFile.Close()

		// Delete old video file if exists
		if existingVideoclase.FilePath != nil {
			if err := h.gcsService.DeleteFile(ctx, *existingVideoclase.FilePath); err != nil {
				// Log error but don't fail the request
				fmt.Printf("Error deleting old video file: %v\n", err)
			}
		}

		// Validate and upload new video
		if !h.validateVideoFile(videoHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid video file type", nil)
			return
		}

		ext := strings.ToLower(filepath.Ext(videoHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("videoclases/%s/%s/%d%s", especialidad, tema, timestamp, ext)
		contentType := fmt.Sprintf("video/%s", strings.TrimPrefix(ext, "."))

		url, err := h.gcsService.UploadFile(ctx, videoFile, videoHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload video", err)
			return
		}

		updateData.URL = &url
		updateData.FilePath = &filePath
	}

	// Handle thumbnail update
	thumbnailFile, thumbnailHeader, err := c.Request.FormFile("thumbnail")
	if err == nil {
		defer thumbnailFile.Close()

		// Validate and upload new thumbnail
		if !h.validateImageFile(thumbnailHeader.Filename) {
			middleware.HandleError(c, http.StatusBadRequest, "Invalid thumbnail file type", nil)
			return
		}

		ext := strings.ToLower(filepath.Ext(thumbnailHeader.Filename))
		timestamp := time.Now().Unix()
		filePath := fmt.Sprintf("thumbnails/%s/%s/%d%s", especialidad, tema, timestamp, ext)
		contentType := fmt.Sprintf("image/%s", strings.TrimPrefix(ext, "."))

		url, err := h.gcsService.UploadFile(ctx, thumbnailFile, thumbnailHeader, filePath, contentType)
		if err != nil {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to upload thumbnail", err)
			return
		}

		updateData.ThumbnailURL = &url
	}

	// Update videoclase
	updatedVideoclase, err := h.repo.UpdateVideoclase(uint(id), updateData)
	if err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to update videoclase", err)
		return
	}

	videoclaseOut := dto.VideoclaseOut{
		ID:           updatedVideoclase.ID,
		Title:        updatedVideoclase.Title,
		Especialidad: updatedVideoclase.Especialidad,
		Sistema:      updatedVideoclase.Sistema,
		Tema:         updatedVideoclase.Tema,
		URL:          updatedVideoclase.URL,
		FilePath:     updatedVideoclase.FilePath,
		ThumbnailURL: updatedVideoclase.ThumbnailURL,
		Duration:     updatedVideoclase.Duration,
		Description:  updatedVideoclase.Description,
		CreatedAt:    updatedVideoclase.CreatedAt,
	}

	response := map[string]interface{}{
		"success":    true,
		"message":    "Videoclase actualizada exitosamente",
		"videoclase": videoclaseOut,
	}

	c.JSON(http.StatusOK, response)
}

// DeleteVideoclase handles deleting a videoclase
func (h *UploadHandler) DeleteVideoclase(c *gin.Context) {
	ctx := context.Background()

	// Get ID from URL
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.HandleError(c, http.StatusBadRequest, "Invalid ID", err)
		return
	}

	// Get existing videoclase to delete associated files
	existingVideoclase, err := h.repo.GetVideoclase(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			middleware.HandleError(c, http.StatusNotFound, "Videoclase not found", err)
		} else {
			middleware.HandleError(c, http.StatusInternalServerError, "Failed to get videoclase", err)
		}
		return
	}

	// Delete associated files from GCS
	if existingVideoclase.FilePath != nil {
		if err := h.gcsService.DeleteFile(ctx, *existingVideoclase.FilePath); err != nil {
			// Log error but don't fail the request
			fmt.Printf("Error deleting video file: %v\n", err)
		}
	}

	// Delete videoclase from database
	if err := h.repo.DeleteVideoclase(uint(id)); err != nil {
		middleware.HandleError(c, http.StatusInternalServerError, "Failed to delete videoclase", err)
		return
	}

	response := map[string]interface{}{
		"success": true,
		"message": "Videoclase eliminada exitosamente",
	}

	c.JSON(http.StatusOK, response)
}
